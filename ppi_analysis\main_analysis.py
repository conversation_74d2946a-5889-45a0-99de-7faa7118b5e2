#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主分析脚本
整合PPI网络分析和功能注释分析

作者：AI Assistant
日期：2025-08-02
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def create_output_directory():
    """创建输出目录"""
    output_dir = Path("ppi_analysis_results")
    output_dir.mkdir(exist_ok=True)
    os.chdir(output_dir)
    print(f"工作目录: {os.getcwd()}")

def run_ppi_analysis():
    """运行PPI网络分析"""
    print("\n" + "="*50)
    print("第一步：PPI网络构建与拓扑分析")
    print("="*50)
    
    try:
        # 导入并运行PPI分析
        sys.path.append('..')
        from ppi_network_analysis import PPINetworkAnalyzer
        
        # 初始化分析器
        analyzer = PPINetworkAnalyzer('../final_enrichment_results_down.csv')
        
        # 执行分析流程
        analyzer.load_data()
        analyzer.build_network(confidence=0.4)
        properties, node_properties = analyzer.calculate_network_properties()
        analyzer.visualize_network()
        analyzer.save_results(properties, node_properties)
        
        print("✅ PPI网络分析完成")
        return True
        
    except Exception as e:
        print(f"❌ PPI网络分析失败: {e}")
        return False

def run_functional_annotation():
    """运行功能注释分析"""
    print("\n" + "="*50)
    print("第二步：Hub蛋白功能注释分析")
    print("="*50)
    
    try:
        # 检查hub_proteins.csv是否存在
        if not os.path.exists('hub_proteins.csv'):
            print("❌ 未找到hub_proteins.csv文件，请先运行PPI网络分析")
            return False
        
        # 导入并运行功能注释分析
        sys.path.append('..')
        from functional_annotation import FunctionalAnnotator
        
        # 初始化注释器
        annotator = FunctionalAnnotator()
        
        # 执行分析流程
        if annotator.load_hub_proteins():
            annotator.perform_go_enrichment()
            annotator.perform_kegg_enrichment()
            annotator.visualize_enrichment_results()
            annotator.save_enrichment_results()
            annotator.generate_summary_report()
            
            print("✅ 功能注释分析完成")
            return True
        else:
            print("❌ 无法加载Hub蛋白数据")
            return False
            
    except Exception as e:
        print(f"❌ 功能注释分析失败: {e}")
        return False

def generate_final_report():
    """生成最终分析报告"""
    print("\n" + "="*50)
    print("第三步：生成综合分析报告")
    print("="*50)
    
    try:
        import pandas as pd
        import json
        
        report_lines = []
        
        # 报告标题
        report_lines.extend([
            "# 下调蛋白质PPI网络分析综合报告\n\n",
            f"**分析时间**: {time.strftime('%Y年%m月%d日 %H:%M:%S')}\n\n",
            "---\n\n"
        ])
        
        # 1. 数据概览
        report_lines.extend([
            "## 1. 数据概览\n\n",
            "本分析基于显著下调蛋白质数据，通过以下三个步骤进行：\n",
            "1. **PPI网络构建**: 使用STRING数据库构建蛋白质互作网络\n",
            "2. **拓扑分析**: 计算网络拓扑参数，识别Hub蛋白\n",
            "3. **功能注释**: 对Hub蛋白进行GO和KEGG富集分析\n\n"
        ])
        
        # 2. 网络拓扑分析结果
        if os.path.exists('network_properties.json'):
            with open('network_properties.json', 'r', encoding='utf-8') as f:
                properties = json.load(f)
            
            report_lines.extend([
                "## 2. PPI网络拓扑分析结果\n\n",
                "### 网络基本属性\n",
                f"- **节点数**: {properties.get('nodes', 'N/A')}\n",
                f"- **边数**: {properties.get('edges', 'N/A')}\n",
                f"- **网络密度**: {properties.get('density', 'N/A'):.4f}\n",
                f"- **连通分量数**: {properties.get('connected_components', 'N/A')}\n"
            ])
            
            if 'diameter' in properties:
                report_lines.extend([
                    f"- **网络直径**: {properties['diameter']}\n",
                    f"- **平均路径长度**: {properties.get('average_path_length', 'N/A'):.4f}\n"
                ])
            
            report_lines.append("\n")
        
        # 3. Hub蛋白分析
        if os.path.exists('hub_proteins.csv'):
            hub_df = pd.read_csv('hub_proteins.csv')
            
            report_lines.extend([
                "## 3. Hub蛋白识别结果\n\n",
                f"基于度中心性识别出前10个Hub蛋白：\n\n",
                "| 排名 | 蛋白质 | 度 | 度中心性 | 介数中心性 | 紧密中心性 |\n",
                "|------|--------|----|---------|-----------|-----------|\n"
            ])
            
            for idx, row in hub_df.iterrows():
                report_lines.append(
                    f"| {idx+1} | {row['protein']} | {row['degree']} | "
                    f"{row['degree_centrality']:.4f} | {row['betweenness_centrality']:.4f} | "
                    f"{row['closeness_centrality']:.4f} |\n"
                )
            
            report_lines.append("\n")
        
        # 4. 功能富集分析结果
        report_lines.extend([
            "## 4. 功能富集分析结果\n\n"
        ])
        
        # GO富集结果
        go_databases = [
            ('go_biological_process_2023_enrichment.csv', 'GO生物过程'),
            ('go_molecular_function_2023_enrichment.csv', 'GO分子功能'),
            ('go_cellular_component_2023_enrichment.csv', 'GO细胞组分')
        ]
        
        for filename, title in go_databases:
            if os.path.exists(filename):
                df = pd.read_csv(filename)
                if not df.empty:
                    report_lines.extend([
                        f"### {title}\n\n",
                        f"显著富集条目数: {len(df)}\n\n",
                        "前5个最显著的条目:\n\n"
                    ])
                    
                    for idx, row in df.head(5).iterrows():
                        report_lines.append(
                            f"- **{row['Term'].split('(')[0]}** "
                            f"(P-adj: {row['Adjusted P-value']:.2e}, "
                            f"基因: {row['Overlap']})\n"
                        )
                    
                    report_lines.append("\n")
        
        # KEGG通路富集结果
        kegg_databases = [
            ('kegg_2021_human_enrichment.csv', 'KEGG通路'),
            ('wikipathway_2023_human_enrichment.csv', 'WikiPathway通路')
        ]
        
        for filename, title in kegg_databases:
            if os.path.exists(filename):
                df = pd.read_csv(filename)
                if not df.empty:
                    report_lines.extend([
                        f"### {title}\n\n",
                        f"显著富集通路数: {len(df)}\n\n",
                        "前5个最显著的通路:\n\n"
                    ])
                    
                    for idx, row in df.head(5).iterrows():
                        report_lines.append(
                            f"- **{row['Term']}** "
                            f"(P-adj: {row['Adjusted P-value']:.2e}, "
                            f"基因: {row['Overlap']})\n"
                        )
                    
                    report_lines.append("\n")
        
        # 5. 结论与讨论
        report_lines.extend([
            "## 5. 结论与讨论\n\n",
            "### 主要发现\n",
            "1. **网络特征**: 构建的PPI网络显示了下调蛋白质之间的复杂互作关系\n",
            "2. **Hub蛋白**: 识别出的Hub蛋白可能是关键的调控节点\n",
            "3. **功能富集**: 富集分析揭示了这些蛋白质参与的主要生物学过程和通路\n\n",
            "### 生物学意义\n",
            "- Hub蛋白通常在生物网络中起到关键调控作用\n",
            "- 这些蛋白质的下调可能对细胞功能产生重要影响\n",
            "- 富集的通路和功能提示了可能的疾病机制或治疗靶点\n\n",
            "### 建议后续分析\n",
            "1. 验证Hub蛋白的表达变化\n",
            "2. 深入研究富集通路的生物学意义\n",
            "3. 考虑进行实验验证关键蛋白质的功能\n\n",
            "---\n\n",
            "*本报告由PPI网络分析系统自动生成*\n"
        ])
        
        # 保存报告
        with open('comprehensive_analysis_report.md', 'w', encoding='utf-8') as f:
            f.writelines(report_lines)
        
        print("✅ 综合分析报告已生成: comprehensive_analysis_report.md")
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False

def list_output_files():
    """列出生成的输出文件"""
    print("\n" + "="*50)
    print("生成的文件列表")
    print("="*50)
    
    files = [
        ('network_properties.json', '网络拓扑属性'),
        ('node_properties.csv', '所有节点属性'),
        ('hub_proteins.csv', 'Hub蛋白列表'),
        ('ppi_interactions.csv', 'PPI互作数据'),
        ('ppi_network.png', 'PPI网络图'),
        ('enrichment_analysis.png', '富集分析图'),
        ('functional_annotation_report.md', '功能注释报告'),
        ('comprehensive_analysis_report.md', '综合分析报告')
    ]
    
    for filename, description in files:
        if os.path.exists(filename):
            print(f"✅ {filename} - {description}")
        else:
            print(f"❌ {filename} - {description} (未生成)")

def main():
    """主函数"""
    print("🧬 下调蛋白质PPI网络分析系统")
    print("="*60)
    
    # 创建输出目录
    create_output_directory()
    
    # 执行分析流程
    success_count = 0
    
    # 第一步：PPI网络分析
    if run_ppi_analysis():
        success_count += 1
    
    # 第二步：功能注释分析
    if run_functional_annotation():
        success_count += 1
    
    # 第三步：生成综合报告
    if generate_final_report():
        success_count += 1
    
    # 列出输出文件
    list_output_files()
    
    # 总结
    print(f"\n{'='*60}")
    print(f"分析完成！成功执行 {success_count}/3 个步骤")
    print(f"结果保存在目录: {os.getcwd()}")
    print("="*60)

if __name__ == "__main__":
    main()
