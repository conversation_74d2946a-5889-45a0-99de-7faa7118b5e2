#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蛋白组差异分析和多元统计分析脚本
包含差异蛋白筛选、PCA分析和PLS-DA分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.cross_decomposition import PLSRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import matplotlib.patches as patches
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ProteinAnalyzer:
    def __init__(self, data_file):
        """初始化分析器"""
        self.data_file = data_file
        self.data = None
        self.diff_proteins = None
        self.hfnef_samples = None
        self.hfsnef_samples = None
        self.expression_data = None
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.data_file)
        print(f"数据加载完成，共{len(self.data)}个蛋白")
        
        # 识别样本列
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        self.hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        self.hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        
        print(f"HFnEF组样本数: {len(self.hfnef_samples)}")
        print(f"HFsnEF组样本数: {len(self.hfsnef_samples)}")
        
    def filter_differential_proteins(self, pvalue_threshold=0.05, fc_threshold=1.5):
        """筛选差异蛋白"""
        print(f"\n正在筛选差异蛋白 (p < {pvalue_threshold}, |FC| > {fc_threshold} 或 < {1/fc_threshold:.3f})...")
        
        # 获取统计列
        fc_col = 'FC(HFsnEF_vs_HFnEF)'
        pvalue_col = 'pValue(HFsnEF_vs_HFnEF)'
        
        # 筛选条件
        pvalue_filter = self.data[pvalue_col] < pvalue_threshold
        fc_up_filter = self.data[fc_col] > fc_threshold
        fc_down_filter = self.data[fc_col] < (1/fc_threshold)
        fc_filter = fc_up_filter | fc_down_filter
        
        # 应用筛选条件
        self.diff_proteins = self.data[pvalue_filter & fc_filter].copy()
        
        print(f"筛选出差异蛋白: {len(self.diff_proteins)}个")
        print(f"上调蛋白: {len(self.diff_proteins[self.diff_proteins[fc_col] > fc_threshold])}个")
        print(f"下调蛋白: {len(self.diff_proteins[self.diff_proteins[fc_col] < (1/fc_threshold)])}个")
        
        return self.diff_proteins
    
    def prepare_expression_data(self):
        """准备表达数据用于多元统计分析"""
        if self.diff_proteins is None:
            raise ValueError("请先筛选差异蛋白")
            
        # 提取差异蛋白的表达数据
        all_samples = self.hfnef_samples + self.hfsnef_samples
        expression_matrix = self.diff_proteins[all_samples].values
        
        # 处理缺失值和异常值
        expression_matrix = np.where(expression_matrix <= 0.001, np.nan, expression_matrix)
        
        # 对数转换
        expression_matrix = np.log2(expression_matrix + 1e-6)
        
        # 填充缺失值（使用行均值）
        for i in range(expression_matrix.shape[0]):
            row = expression_matrix[i, :]
            if np.any(np.isnan(row)):
                row_mean = np.nanmean(row)
                row[np.isnan(row)] = row_mean
        
        self.expression_data = expression_matrix.T  # 转置，样本为行，蛋白为列
        
        # 创建分组标签
        self.group_labels = (['HFnEF'] * len(self.hfnef_samples) + 
                           ['HFsnEF'] * len(self.hfsnef_samples))
        
        print(f"表达数据准备完成: {self.expression_data.shape[0]}个样本, {self.expression_data.shape[1]}个差异蛋白")
        
    def perform_pca(self):
        """执行PCA分析"""
        print("\n正在进行PCA分析...")
        
        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(self.expression_data)
        
        # PCA分析
        pca = PCA()
        pca_result = pca.fit_transform(scaled_data)
        
        # 计算贡献率
        explained_variance_ratio = pca.explained_variance_ratio_
        pc1_contribution = explained_variance_ratio[0] * 100
        pc2_contribution = explained_variance_ratio[1] * 100
        
        print(f"PC1贡献率: {pc1_contribution:.2f}%")
        print(f"PC2贡献率: {pc2_contribution:.2f}%")
        print(f"PC1+PC2累计贡献率: {pc1_contribution + pc2_contribution:.2f}%")
        
        # 绘制PCA图
        self.plot_pca(pca_result, pc1_contribution, pc2_contribution)
        
        return pca_result, pc1_contribution, pc2_contribution
    
    def plot_pca(self, pca_result, pc1_contrib, pc2_contrib):
        """绘制PCA散点图"""
        plt.figure(figsize=(10, 8))
        
        # 分组数据
        hfnef_indices = [i for i, label in enumerate(self.group_labels) if label == 'HFnEF']
        hfsnef_indices = [i for i, label in enumerate(self.group_labels) if label == 'HFsnEF']
        
        # 绘制散点
        plt.scatter(pca_result[hfnef_indices, 0], pca_result[hfnef_indices, 1], 
                   c='blue', alpha=0.7, s=60, label='HFnEF', edgecolors='black', linewidth=0.5)
        plt.scatter(pca_result[hfsnef_indices, 0], pca_result[hfsnef_indices, 1], 
                   c='red', alpha=0.7, s=60, label='HFsnEF', edgecolors='black', linewidth=0.5)
        
        # 计算并绘制95%置信区间椭圆
        self.plot_confidence_ellipse(pca_result[hfnef_indices, 0], pca_result[hfnef_indices, 1], 
                                    'blue', alpha=0.2)
        self.plot_confidence_ellipse(pca_result[hfsnef_indices, 0], pca_result[hfsnef_indices, 1], 
                                    'red', alpha=0.2)
        
        plt.xlabel(f'PC1 ({pc1_contrib:.2f}%)', fontsize=12)
        plt.ylabel(f'PC2 ({pc2_contrib:.2f}%)', fontsize=12)
        plt.title('PCA分析 - 差异蛋白表达谱', fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('PCA_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_confidence_ellipse(self, x, y, color, alpha=0.2):
        """绘制95%置信区间椭圆"""
        if len(x) < 3:  # 需要至少3个点来计算椭圆
            return
            
        # 计算均值和协方差矩阵
        mean_x, mean_y = np.mean(x), np.mean(y)
        cov_matrix = np.cov(x, y)
        
        # 计算特征值和特征向量
        eigenvals, eigenvecs = np.linalg.eigh(cov_matrix)
        
        # 计算椭圆参数
        # 95%置信区间对应卡方分布的0.95分位数
        chi2_val = stats.chi2.ppf(0.95, df=2)
        
        # 椭圆的长短轴
        width = 2 * np.sqrt(chi2_val * eigenvals[1])
        height = 2 * np.sqrt(chi2_val * eigenvals[0])
        
        # 椭圆的旋转角度
        angle = np.degrees(np.arctan2(eigenvecs[1, 1], eigenvecs[0, 1]))
        
        # 创建椭圆
        ellipse = patches.Ellipse((mean_x, mean_y), width, height, 
                                angle=angle, facecolor=color, alpha=alpha, 
                                edgecolor=color, linewidth=2)
        
        plt.gca().add_patch(ellipse)

    def perform_plsda(self):
        """执行PLS-DA分析"""
        print("\n正在进行PLS-DA分析...")

        # 准备标签（数值化）
        y = np.array([0 if label == 'HFnEF' else 1 for label in self.group_labels])

        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(self.expression_data)

        # PLS-DA分析
        plsda = PLSRegression(n_components=2, scale=False)
        plsda_result = plsda.fit_transform(scaled_data, y)[0]

        # 计算R2和Q2
        r2_scores = []
        q2_scores = []

        for i in range(1, 3):  # 计算前2个成分
            pls_temp = PLSRegression(n_components=i, scale=False)
            pls_temp.fit(scaled_data, y)

            # R2 (拟合优度)
            y_pred = pls_temp.predict(scaled_data)
            r2 = 1 - np.sum((y.reshape(-1, 1) - y_pred)**2) / np.sum((y.reshape(-1, 1) - np.mean(y))**2)
            r2_scores.append(r2)

            # Q2 (预测能力) - 交叉验证
            cv_scores = cross_val_score(pls_temp, scaled_data, y, cv=5, scoring='r2')
            q2_scores.append(np.mean(cv_scores))

        print(f"PLS-DA Component 1 - R2: {r2_scores[0]:.3f}, Q2: {q2_scores[0]:.3f}")
        print(f"PLS-DA Component 2 - R2: {r2_scores[1]:.3f}, Q2: {q2_scores[1]:.3f}")

        # 计算各成分的方差解释率
        total_var = np.var(plsda_result, axis=0)
        comp1_contrib = (total_var[0] / np.sum(total_var)) * 100
        comp2_contrib = (total_var[1] / np.sum(total_var)) * 100

        print(f"PLS-DA Component 1贡献率: {comp1_contrib:.2f}%")
        print(f"PLS-DA Component 2贡献率: {comp2_contrib:.2f}%")

        # 绘制PLS-DA图
        self.plot_plsda(plsda_result, comp1_contrib, comp2_contrib, r2_scores, q2_scores)

        return plsda_result, comp1_contrib, comp2_contrib, r2_scores, q2_scores

    def plot_plsda(self, plsda_result, comp1_contrib, comp2_contrib, r2_scores, q2_scores):
        """绘制PLS-DA散点图"""
        plt.figure(figsize=(10, 8))

        # 分组数据
        hfnef_indices = [i for i, label in enumerate(self.group_labels) if label == 'HFnEF']
        hfsnef_indices = [i for i, label in enumerate(self.group_labels) if label == 'HFsnEF']

        # 绘制散点
        plt.scatter(plsda_result[hfnef_indices, 0], plsda_result[hfnef_indices, 1],
                   c='blue', alpha=0.7, s=60, label='HFnEF', edgecolors='black', linewidth=0.5)
        plt.scatter(plsda_result[hfsnef_indices, 0], plsda_result[hfsnef_indices, 1],
                   c='red', alpha=0.7, s=60, label='HFsnEF', edgecolors='black', linewidth=0.5)

        # 计算并绘制95%置信区间椭圆
        self.plot_confidence_ellipse(plsda_result[hfnef_indices, 0], plsda_result[hfnef_indices, 1],
                                    'blue', alpha=0.2)
        self.plot_confidence_ellipse(plsda_result[hfsnef_indices, 0], plsda_result[hfsnef_indices, 1],
                                    'red', alpha=0.2)

        plt.xlabel(f'Component 1 ({comp1_contrib:.2f}%)', fontsize=12)
        plt.ylabel(f'Component 2 ({comp2_contrib:.2f}%)', fontsize=12)
        plt.title(f'PLS-DA分析 - 差异蛋白表达谱\nR2: {r2_scores[1]:.3f}, Q2: {q2_scores[1]:.3f}',
                 fontsize=14, fontweight='bold')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('PLSDA_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_report(self, pca_result, pca_contrib, plsda_result, plsda_contrib, r2_scores, q2_scores):
        """生成分析报告"""
        print("\n" + "="*60)
        print("蛋白组差异分析报告")
        print("="*60)

        # 基本信息
        print(f"\n1. 数据概况:")
        print(f"   总蛋白数: {len(self.data)}")
        print(f"   HFnEF组样本数: {len(self.hfnef_samples)}")
        print(f"   HFsnEF组样本数: {len(self.hfsnef_samples)}")

        # 差异蛋白信息
        fc_col = 'FC(HFsnEF_vs_HFnEF)'
        up_proteins = len(self.diff_proteins[self.diff_proteins[fc_col] > 1.5])
        down_proteins = len(self.diff_proteins[self.diff_proteins[fc_col] < (1/1.5)])

        print(f"\n2. 差异蛋白筛选结果:")
        print(f"   筛选条件: p < 0.05, |FC| > 1.5 或 < 0.667")
        print(f"   差异蛋白总数: {len(self.diff_proteins)}")
        print(f"   上调蛋白: {up_proteins} ({up_proteins/len(self.diff_proteins)*100:.1f}%)")
        print(f"   下调蛋白: {down_proteins} ({down_proteins/len(self.diff_proteins)*100:.1f}%)")

        # PCA结果
        print(f"\n3. PCA分析结果:")
        print(f"   PC1贡献率: {pca_contrib[0]:.2f}%")
        print(f"   PC2贡献率: {pca_contrib[1]:.2f}%")
        print(f"   累计贡献率: {sum(pca_contrib[:2]):.2f}%")

        # PLS-DA结果
        print(f"\n4. PLS-DA分析结果:")
        print(f"   Component 1贡献率: {plsda_contrib[0]:.2f}%")
        print(f"   Component 2贡献率: {plsda_contrib[1]:.2f}%")
        print(f"   模型拟合度(R2): {r2_scores[1]:.3f}")
        print(f"   预测能力(Q2): {q2_scores[1]:.3f}")

        # 模型评价
        print(f"\n5. 模型评价:")
        if q2_scores[1] > 0.5:
            print("   PLS-DA模型预测能力优秀 (Q2 > 0.5)")
        elif q2_scores[1] > 0.4:
            print("   PLS-DA模型预测能力良好 (Q2 > 0.4)")
        else:
            print("   PLS-DA模型预测能力一般 (Q2 < 0.4)")

        if r2_scores[1] > 0.7:
            print("   模型拟合度优秀 (R2 > 0.7)")
        elif r2_scores[1] > 0.5:
            print("   模型拟合度良好 (R2 > 0.5)")
        else:
            print("   模型拟合度一般 (R2 < 0.5)")

        print(f"\n6. 输出文件:")
        print("   PCA_analysis.png - PCA分析散点图")
        print("   PLSDA_analysis.png - PLS-DA分析散点图")
        print("   differential_proteins.csv - 差异蛋白列表")

        # 保存差异蛋白列表
        output_cols = ['ID', 'Accession', 'Gene Name', 'Description',
                      'FC(HFsnEF_vs_HFnEF)', 'pValue(HFsnEF_vs_HFnEF)',
                      'FDR(HFsnEF_vs_HFnEF)', 'Sig(HFsnEF_vs_HFnEF)']
        self.diff_proteins[output_cols].to_csv('differential_proteins.csv', index=False)

        print("\n分析完成！")
        print("="*60)

def main():
    """主函数 - 执行完整的分析流程"""
    # 初始化分析器
    analyzer = ProteinAnalyzer('Proteins_all_diff.csv')

    try:
        # 1. 加载数据
        analyzer.load_data()

        # 2. 筛选差异蛋白
        diff_proteins = analyzer.filter_differential_proteins(pvalue_threshold=0.05, fc_threshold=1.5)

        if len(diff_proteins) == 0:
            print("未找到符合条件的差异蛋白，请检查筛选条件")
            return

        # 3. 准备表达数据
        analyzer.prepare_expression_data()

        # 4. PCA分析
        pca_result, pc1_contrib, pc2_contrib = analyzer.perform_pca()

        # 5. PLS-DA分析
        plsda_result, comp1_contrib, comp2_contrib, r2_scores, q2_scores = analyzer.perform_plsda()

        # 6. 生成报告
        analyzer.generate_report(
            pca_result, (pc1_contrib, pc2_contrib),
            plsda_result, (comp1_contrib, comp2_contrib),
            r2_scores, q2_scores
        )

        # 7. 显示前10个差异最显著的蛋白
        print("\n差异最显著的前10个蛋白:")
        print("-" * 80)
        top_proteins = diff_proteins.nsmallest(10, 'pValue(HFsnEF_vs_HFnEF)')
        for idx, row in top_proteins.iterrows():
            fc = row['FC(HFsnEF_vs_HFnEF)']
            pval = row['pValue(HFsnEF_vs_HFnEF)']
            gene_name = row['Gene Name']
            description = row['Description'][:50] + "..." if len(row['Description']) > 50 else row['Description']
            regulation = "上调" if fc > 1 else "下调"
            print(f"{gene_name:15} | FC: {fc:6.2f} | p: {pval:.2e} | {regulation} | {description}")

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
