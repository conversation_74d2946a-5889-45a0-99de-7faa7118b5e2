#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建蛋白组分析结果的汇总图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def create_summary_plots():
    """创建汇总图表"""
    
    # 读取差异蛋白数据
    diff_proteins = pd.read_csv('differential_proteins.csv')
    
    # 创建2x2的子图布局
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('蛋白组差异分析结果汇总', fontsize=16, fontweight='bold', y=0.95)
    
    # 1. 差异蛋白数量统计柱状图
    ax1 = axes[0, 0]
    up_count = len(diff_proteins[diff_proteins['FC(HFsnEF_vs_HFnEF)'] > 1.5])
    down_count = len(diff_proteins[diff_proteins['FC(HFsnEF_vs_HFnEF)'] < (1/1.5)])
    
    categories = ['上调蛋白', '下调蛋白']
    counts = [up_count, down_count]
    colors = ['#FF6B6B', '#4ECDC4']
    
    bars = ax1.bar(categories, counts, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    ax1.set_title('差异蛋白数量分布', fontsize=14, fontweight='bold')
    ax1.set_ylabel('蛋白数量', fontsize=12)
    
    # 添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{count}\n({count/(up_count+down_count)*100:.1f}%)',
                ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    ax1.set_ylim(0, max(counts) * 1.2)
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. FC分布直方图
    ax2 = axes[0, 1]
    fc_values = diff_proteins['FC(HFsnEF_vs_HFnEF)']
    log2_fc = np.log2(fc_values)
    
    ax2.hist(log2_fc, bins=30, color='skyblue', alpha=0.7, edgecolor='black', linewidth=0.5)
    ax2.axvline(x=np.log2(1.5), color='red', linestyle='--', linewidth=2, label='FC=1.5')
    ax2.axvline(x=np.log2(1/1.5), color='red', linestyle='--', linewidth=2, label='FC=0.67')
    ax2.axvline(x=0, color='gray', linestyle='-', linewidth=1, alpha=0.5)
    
    ax2.set_title('倍数变化(FC)分布', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Log2(FC)', fontsize=12)
    ax2.set_ylabel('蛋白数量', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. p值分布直方图
    ax3 = axes[1, 0]
    p_values = diff_proteins['pValue(HFsnEF_vs_HFnEF)']
    log10_p = -np.log10(p_values)
    
    ax3.hist(log10_p, bins=30, color='lightcoral', alpha=0.7, edgecolor='black', linewidth=0.5)
    ax3.axvline(x=-np.log10(0.05), color='red', linestyle='--', linewidth=2, label='p=0.05')
    ax3.axvline(x=-np.log10(0.01), color='orange', linestyle='--', linewidth=2, label='p=0.01')
    
    ax3.set_title('p值分布', fontsize=14, fontweight='bold')
    ax3.set_xlabel('-Log10(p值)', fontsize=12)
    ax3.set_ylabel('蛋白数量', fontsize=12)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 火山图
    ax4 = axes[1, 1]
    
    # 准备火山图数据
    x = log2_fc
    y = log10_p
    
    # 根据显著性和FC着色
    colors = []
    for fc, p in zip(fc_values, p_values):
        if p < 0.05 and fc > 1.5:
            colors.append('#FF6B6B')  # 显著上调 - 红色
        elif p < 0.05 and fc < (1/1.5):
            colors.append('#4ECDC4')  # 显著下调 - 青色
        else:
            colors.append('#CCCCCC')  # 不显著 - 灰色
    
    scatter = ax4.scatter(x, y, c=colors, alpha=0.6, s=20, edgecolors='black', linewidth=0.3)
    
    # 添加阈值线
    ax4.axhline(y=-np.log10(0.05), color='red', linestyle='--', linewidth=1, alpha=0.7)
    ax4.axvline(x=np.log2(1.5), color='red', linestyle='--', linewidth=1, alpha=0.7)
    ax4.axvline(x=np.log2(1/1.5), color='red', linestyle='--', linewidth=1, alpha=0.7)
    
    ax4.set_title('火山图', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Log2(FC)', fontsize=12)
    ax4.set_ylabel('-Log10(p值)', fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#FF6B6B', label=f'显著上调 ({up_count})'),
        Patch(facecolor='#4ECDC4', label=f'显著下调 ({down_count})'),
        Patch(facecolor='#CCCCCC', label='不显著')
    ]
    ax4.legend(handles=legend_elements, loc='upper right')
    
    plt.tight_layout()
    plt.savefig('蛋白组分析结果汇总.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 创建前20个最显著差异蛋白的条形图
    create_top_proteins_plot(diff_proteins)

def create_top_proteins_plot(diff_proteins):
    """创建前20个最显著差异蛋白的条形图"""
    
    # 选择前20个最显著的蛋白
    top_proteins = diff_proteins.nsmallest(20, 'pValue(HFsnEF_vs_HFnEF)')
    
    plt.figure(figsize=(12, 10))
    
    # 准备数据
    gene_names = top_proteins['Gene Name'].values
    log2_fc = np.log2(top_proteins['FC(HFsnEF_vs_HFnEF)'].values)
    p_values = top_proteins['pValue(HFsnEF_vs_HFnEF)'].values
    
    # 根据FC方向着色
    colors = ['#FF6B6B' if fc > 0 else '#4ECDC4' for fc in log2_fc]
    
    # 创建水平条形图
    y_pos = np.arange(len(gene_names))
    bars = plt.barh(y_pos, log2_fc, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 设置y轴标签
    plt.yticks(y_pos, gene_names)
    plt.xlabel('Log2(FC)', fontsize=12)
    plt.title('前20个最显著差异蛋白', fontsize=14, fontweight='bold')
    
    # 添加p值信息
    for i, (bar, p_val) in enumerate(zip(bars, p_values)):
        width = bar.get_width()
        plt.text(width + 0.1 if width > 0 else width - 0.1, bar.get_y() + bar.get_height()/2,
                f'p={p_val:.2e}', ha='left' if width > 0 else 'right', va='center', fontsize=8)
    
    # 添加垂直参考线
    plt.axvline(x=0, color='black', linestyle='-', linewidth=1, alpha=0.5)
    plt.axvline(x=np.log2(1.5), color='red', linestyle='--', linewidth=1, alpha=0.7)
    plt.axvline(x=np.log2(1/1.5), color='red', linestyle='--', linewidth=1, alpha=0.7)
    
    # 添加图例
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#FF6B6B', label='上调'),
        Patch(facecolor='#4ECDC4', label='下调')
    ]
    plt.legend(handles=legend_elements, loc='lower right')
    
    plt.grid(True, alpha=0.3, axis='x')
    plt.tight_layout()
    plt.savefig('前20个最显著差异蛋白.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_analysis_summary():
    """打印分析结果摘要"""
    diff_proteins = pd.read_csv('differential_proteins.csv')
    
    print("\n" + "="*60)
    print("蛋白组差异分析结果摘要")
    print("="*60)
    
    total_proteins = len(diff_proteins)
    up_proteins = len(diff_proteins[diff_proteins['FC(HFsnEF_vs_HFnEF)'] > 1.5])
    down_proteins = len(diff_proteins[diff_proteins['FC(HFsnEF_vs_HFnEF)'] < (1/1.5)])
    
    print(f"差异蛋白总数: {total_proteins}")
    print(f"上调蛋白: {up_proteins} ({up_proteins/total_proteins*100:.1f}%)")
    print(f"下调蛋白: {down_proteins} ({down_proteins/total_proteins*100:.1f}%)")
    
    # FC统计
    fc_values = diff_proteins['FC(HFsnEF_vs_HFnEF)']
    print(f"\nFC统计:")
    print(f"最大FC: {fc_values.max():.2f}")
    print(f"最小FC: {fc_values.min():.2f}")
    print(f"FC中位数: {fc_values.median():.2f}")
    
    # p值统计
    p_values = diff_proteins['pValue(HFsnEF_vs_HFnEF)']
    print(f"\np值统计:")
    print(f"最小p值: {p_values.min():.2e}")
    print(f"p值中位数: {p_values.median():.2e}")
    print(f"p < 0.01的蛋白数: {len(diff_proteins[p_values < 0.01])}")
    print(f"p < 0.001的蛋白数: {len(diff_proteins[p_values < 0.001])}")
    
    print("="*60)

if __name__ == "__main__":
    print("正在创建汇总图表...")
    create_summary_plots()
    print_analysis_summary()
    print("图表创建完成！")
