Proteins_all_diff.csv
上面这个csv文件是蛋白组数据集，第一行是分组名称HFnEF和HFsnEF用连接符加样本编号，下面每一行代表一个蛋白，第一列是蛋白编号，第二列是蛋白Accession，第三列是蛋白的Gene Name，第四列是蛋白的Description，最后几列是FC(HFsnEF_vs_HFnEF) ，pValue(HFsnEF_vs_HFnEF)， FDR(HFsnEF_vs_HFnEF) ，Sig(HFsnEF_vs_HFnEF)，CC，MF BP，KEGG，其余每一列代表一个样本。
分析需求：
1. KEGG那一列是空的，所以你应该先对所有蛋白进行KEGG注释，再针对筛选出的所有差异蛋白，分别进行上调蛋白和下调蛋白的功能和KEGG聚类分析，包括细胞BP、CC，MF分析。GO和KEGG富集分析散点图：按照富集度（显著性）排序选取top20的条目来绘制气泡图。纵坐标表示条目分类条目，横坐标表示每个term中富集到的蛋白占该term总蛋白的比例。圆圈代表每个term富集到的蛋白，圆圈大小代表蛋白数目的多少，越大表示数量越多；圆圈颜色表示term的显著性（-log10(Pvalue)），颜色越红，表示term越具有显著性。
2. 做BP和KEGG网络互作图。

请先详细描述你的分析过程方案，然后再开始进行脚本编写分析，最后生成报告。所有生成的文件新建一个文件夹放置。