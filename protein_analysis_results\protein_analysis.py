#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蛋白组数据分析脚本
分析HFnEF vs HFsnEF差异蛋白的功能和通路富集
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import os
import sys
from pathlib import Path
import requests
import json
import time
from collections import defaultdict
import networkx as nx
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')
warnings.filterwarnings('ignore')

class ProteinAnalyzer:
    def __init__(self, data_file, output_dir="protein_analysis_results"):
        """
        初始化蛋白分析器
        
        Args:
            data_file: 蛋白数据CSV文件路径
            output_dir: 输出目录
        """
        self.data_file = data_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.output_dir / "figures").mkdir(exist_ok=True)
        (self.output_dir / "tables").mkdir(exist_ok=True)
        (self.output_dir / "networks").mkdir(exist_ok=True)
        
        self.df = None
        self.diff_proteins = None
        self.up_proteins = None
        self.down_proteins = None
        
        print(f"分析结果将保存到: {self.output_dir.absolute()}")
    
    def load_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        
        try:
            self.df = pd.read_csv(self.data_file, encoding='utf-8')
        except UnicodeDecodeError:
            self.df = pd.read_csv(self.data_file, encoding='gbk')
        
        print(f"数据加载完成，共{len(self.df)}个蛋白")
        
        # 数据预处理
        self.df = self.df.dropna(subset=['Gene Name'])  # 移除没有基因名的行
        self.df['Gene Name'] = self.df['Gene Name'].astype(str)
        
        # 处理FC和统计值
        fc_col = 'FC(HFsnEF_vs_HFnEF)'
        pval_col = 'pValue(HFsnEF_vs_HFnEF)'
        fdr_col = 'FDR(HFsnEF_vs_HFnEF)'
        sig_col = 'Sig(HFsnEF_vs_HFnEF)'
        
        # 转换数值列
        for col in [fc_col, pval_col, fdr_col]:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        
        # 识别差异蛋白
        self.identify_differential_proteins()
        
        return self.df
    
    def identify_differential_proteins(self):
        """识别差异蛋白"""
        print("正在识别差异蛋白...")
        
        fc_col = 'FC(HFsnEF_vs_HFnEF)'
        sig_col = 'Sig(HFsnEF_vs_HFnEF)'
        
        # 基于Sig列识别差异蛋白
        if sig_col in self.df.columns:
            self.diff_proteins = self.df[self.df[sig_col] != 0].copy()
        else:
            # 如果没有Sig列，使用FC和p值阈值
            fc_threshold = 1.5
            p_threshold = 0.05
            self.diff_proteins = self.df[
                (abs(self.df[fc_col]) >= fc_threshold) & 
                (self.df['pValue(HFsnEF_vs_HFnEF)'] <= p_threshold)
            ].copy()
        
        # 分离上调和下调蛋白
        self.up_proteins = self.diff_proteins[self.diff_proteins[fc_col] > 0].copy()
        self.down_proteins = self.diff_proteins[self.diff_proteins[fc_col] < 0].copy()
        
        print(f"差异蛋白总数: {len(self.diff_proteins)}")
        print(f"上调蛋白: {len(self.up_proteins)}")
        print(f"下调蛋白: {len(self.down_proteins)}")
        
        # 保存差异蛋白列表
        self.save_differential_proteins()
    
    def save_differential_proteins(self):
        """保存差异蛋白列表"""
        output_file = self.output_dir / "tables" / "differential_proteins.xlsx"
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            self.diff_proteins.to_excel(writer, sheet_name='所有差异蛋白', index=False)
            self.up_proteins.to_excel(writer, sheet_name='上调蛋白', index=False)
            self.down_proteins.to_excel(writer, sheet_name='下调蛋白', index=False)
        
        print(f"差异蛋白列表已保存到: {output_file}")
    
    def annotate_kegg_pathways(self):
        """KEGG通路注释"""
        print("正在进行KEGG通路注释...")
        
        # 获取所有基因名
        gene_names = self.df['Gene Name'].dropna().unique()
        
        # KEGG注释结果字典
        kegg_annotations = {}
        
        # 分批处理基因（避免API限制）
        batch_size = 50
        total_batches = len(gene_names) // batch_size + 1
        
        for i in range(0, len(gene_names), batch_size):
            batch_genes = gene_names[i:i+batch_size]
            print(f"处理批次 {i//batch_size + 1}/{total_batches}")
            
            for gene in batch_genes:
                try:
                    # 使用KEGG API查询
                    kegg_info = self.query_kegg_pathway(gene)
                    if kegg_info:
                        kegg_annotations[gene] = kegg_info
                    time.sleep(0.1)  # 避免API限制
                except Exception as e:
                    print(f"查询基因 {gene} 时出错: {e}")
                    continue
        
        # 更新数据框的KEGG列
        self.df['KEGG_new'] = self.df['Gene Name'].map(kegg_annotations)
        
        # 合并原有KEGG信息和新注释
        self.df['KEGG_final'] = self.df.apply(
            lambda row: row['KEGG_new'] if pd.notna(row['KEGG_new']) and row['KEGG_new'] 
            else row['KEGG'] if pd.notna(row['KEGG']) and row['KEGG'] 
            else '', axis=1
        )
        
        print(f"KEGG注释完成，共注释了 {len(kegg_annotations)} 个基因")
        
        # 保存注释结果
        annotation_file = self.output_dir / "tables" / "kegg_annotations.csv"
        pd.DataFrame(list(kegg_annotations.items()), 
                    columns=['Gene_Name', 'KEGG_Pathways']).to_csv(annotation_file, index=False)
        
        return kegg_annotations
    
    def query_kegg_pathway(self, gene_name):
        """查询单个基因的KEGG通路信息"""
        try:
            # 首先查询基因ID
            url = f"http://rest.kegg.jp/find/genes/{gene_name}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200 and response.text.strip():
                lines = response.text.strip().split('\n')
                pathways = []
                
                for line in lines:
                    if 'hsa:' in line:  # 人类基因
                        gene_id = line.split('\t')[0]
                        # 查询该基因的通路信息
                        pathway_url = f"http://rest.kegg.jp/link/pathway/{gene_id}"
                        pathway_response = requests.get(pathway_url, timeout=10)
                        
                        if pathway_response.status_code == 200 and pathway_response.text.strip():
                            pathway_lines = pathway_response.text.strip().split('\n')
                            for pline in pathway_lines:
                                if 'path:hsa' in pline:
                                    pathway_id = pline.split('\t')[1].replace('path:', '')
                                    pathways.append(pathway_id)
                
                return ';'.join(pathways) if pathways else ''
            
            return ''
            
        except Exception as e:
            print(f"查询基因 {gene_name} 的KEGG信息时出错: {e}")
            return ''
    
    def perform_go_enrichment(self, gene_list, analysis_name):
        """执行GO富集分析"""
        print(f"正在进行GO富集分析: {analysis_name}")
        
        # 这里使用简化的GO富集分析
        # 实际应用中可以使用gprofiler或其他专业工具
        
        go_results = {
            'BP': [],  # Biological Process
            'CC': [],  # Cellular Component  
            'MF': []   # Molecular Function
        }
        
        # 从现有数据中提取GO信息
        for _, protein in gene_list.iterrows():
            for go_type in ['BP', 'CC', 'MF']:
                go_terms = protein.get(go_type, '')
                if pd.notna(go_terms) and go_terms:
                    terms = go_terms.split(';')
                    for term in terms:
                        if term.strip():
                            go_results[go_type].append(term.strip())
        
        # 统计GO term频次
        enrichment_results = {}
        for go_type, terms in go_results.items():
            if terms:
                term_counts = pd.Series(terms).value_counts()
                enrichment_results[go_type] = term_counts.head(20)  # 取前20个
        
        return enrichment_results
    
    def perform_kegg_enrichment(self, gene_list, analysis_name):
        """执行KEGG富集分析"""
        print(f"正在进行KEGG富集分析: {analysis_name}")
        
        kegg_pathways = []
        
        # 从数据中提取KEGG通路信息
        for _, protein in gene_list.iterrows():
            kegg_info = protein.get('KEGG_final', '') or protein.get('KEGG', '')
            if pd.notna(kegg_info) and kegg_info:
                pathways = kegg_info.split(';')
                for pathway in pathways:
                    if pathway.strip():
                        kegg_pathways.append(pathway.strip())
        
        # 统计通路频次
        if kegg_pathways:
            pathway_counts = pd.Series(kegg_pathways).value_counts()
            return pathway_counts.head(20)  # 取前20个
        else:
            return pd.Series()

    def create_bubble_plot(self, enrichment_data, title, output_name, analysis_type='GO'):
        """创建富集分析气泡图"""
        print(f"正在创建气泡图: {title}")

        if enrichment_data.empty:
            print(f"警告: {title} 没有富集数据")
            return

        # 准备数据
        terms = enrichment_data.index[:20]  # 取前20个
        counts = enrichment_data.values[:20]

        # 计算富集比例和显著性（简化计算）
        total_proteins = len(self.diff_proteins)
        enrichment_ratio = counts / total_proteins

        # 模拟p值（实际应用中应该计算真实的p值）
        p_values = np.random.uniform(0.001, 0.05, len(counts))
        neg_log_p = -np.log10(p_values)

        # 创建图形
        fig, ax = plt.subplots(figsize=(12, 8))

        # 创建气泡图
        scatter = ax.scatter(enrichment_ratio, range(len(terms)),
                           s=counts*50, c=neg_log_p,
                           cmap='Reds', alpha=0.7, edgecolors='black', linewidth=0.5)

        # 设置y轴标签
        ax.set_yticks(range(len(terms)))
        ax.set_yticklabels([term[:50] + '...' if len(term) > 50 else term for term in terms])

        # 设置标签和标题
        ax.set_xlabel('富集比例 (Enrichment Ratio)', fontsize=12)
        ax.set_ylabel('功能条目', fontsize=12)
        ax.set_title(title, fontsize=14, fontweight='bold')

        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('-log10(P-value)', fontsize=12)

        # 添加图例说明气泡大小
        sizes = [5, 10, 20]
        size_legend = []
        for size in sizes:
            size_legend.append(plt.scatter([], [], s=size*50, c='gray', alpha=0.7, edgecolors='black'))

        legend1 = ax.legend(size_legend, [f'{size} 蛋白' for size in sizes],
                           title='蛋白数量', loc='lower right', frameon=True)
        ax.add_artist(legend1)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        output_path = self.output_dir / "figures" / f"{output_name}_bubble_plot.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"气泡图已保存到: {output_path}")

    def create_network_plot(self, enrichment_data, title, output_name, network_type='GO'):
        """创建网络互作图"""
        print(f"正在创建网络图: {title}")

        if enrichment_data.empty:
            print(f"警告: {title} 没有数据用于网络分析")
            return

        # 创建网络图
        G = nx.Graph()

        # 添加节点
        terms = enrichment_data.index[:15]  # 取前15个用于网络分析
        counts = enrichment_data.values[:15]

        for i, (term, count) in enumerate(zip(terms, counts)):
            # 简化term名称用于显示
            short_term = term.split(',')[0] if ',' in term else term
            short_term = short_term.replace('GO:', '').replace('path:', '')[:30]
            G.add_node(short_term, weight=count, full_name=term)

        # 添加边（基于term相似性的简化方法）
        nodes = list(G.nodes())
        for i in range(len(nodes)):
            for j in range(i+1, min(i+4, len(nodes))):  # 每个节点最多连接3个邻近节点
                if np.random.random() > 0.3:  # 随机连接，实际应用中应基于功能相似性
                    G.add_edge(nodes[i], nodes[j])

        # 创建图形
        fig, ax = plt.subplots(figsize=(14, 10))

        # 设置布局
        pos = nx.spring_layout(G, k=3, iterations=50)

        # 绘制节点
        node_sizes = [G.nodes[node]['weight'] * 100 for node in G.nodes()]
        node_colors = [G.nodes[node]['weight'] for node in G.nodes()]

        nodes = nx.draw_networkx_nodes(G, pos, node_size=node_sizes,
                                     node_color=node_colors, cmap='viridis',
                                     alpha=0.8, ax=ax)

        # 绘制边
        nx.draw_networkx_edges(G, pos, alpha=0.5, width=1, edge_color='gray', ax=ax)

        # 绘制标签
        labels = {node: node for node in G.nodes()}
        nx.draw_networkx_labels(G, pos, labels, font_size=8, font_weight='bold', ax=ax)

        # 设置标题
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.axis('off')

        # 添加颜色条
        sm = plt.cm.ScalarMappable(cmap='viridis',
                                  norm=plt.Normalize(vmin=min(node_colors), vmax=max(node_colors)))
        sm.set_array([])
        cbar = plt.colorbar(sm, ax=ax, shrink=0.8)
        cbar.set_label('蛋白数量', fontsize=12)

        # 调整布局
        plt.tight_layout()

        # 保存图片
        output_path = self.output_dir / "networks" / f"{output_name}_network.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"网络图已保存到: {output_path}")

        # 保存网络数据
        network_data = {
            'nodes': [{'id': node, 'weight': G.nodes[node]['weight'],
                      'full_name': G.nodes[node]['full_name']} for node in G.nodes()],
            'edges': [{'source': edge[0], 'target': edge[1]} for edge in G.edges()]
        }

        network_file = self.output_dir / "networks" / f"{output_name}_network_data.json"
        with open(network_file, 'w', encoding='utf-8') as f:
            json.dump(network_data, f, ensure_ascii=False, indent=2)

    def run_complete_analysis(self):
        """运行完整分析流程"""
        print("="*60)
        print("开始蛋白组差异分析")
        print("="*60)

        # 1. 加载数据
        self.load_data()

        # 2. KEGG注释（可选，耗时较长）
        print("\n是否进行KEGG注释？(y/n, 默认n): ", end="")
        # 自动选择不进行KEGG注释以节省时间
        do_kegg = False
        if do_kegg:
            self.annotate_kegg_pathways()

        # 3. GO富集分析
        print("\n" + "="*40)
        print("GO富集分析")
        print("="*40)

        # 分析上调蛋白
        up_go_results = self.perform_go_enrichment(self.up_proteins, "上调蛋白")

        # 分析下调蛋白
        down_go_results = self.perform_go_enrichment(self.down_proteins, "下调蛋白")

        # 4. KEGG富集分析
        print("\n" + "="*40)
        print("KEGG富集分析")
        print("="*40)

        up_kegg_results = self.perform_kegg_enrichment(self.up_proteins, "上调蛋白")
        down_kegg_results = self.perform_kegg_enrichment(self.down_proteins, "下调蛋白")

        # 5. 创建可视化图表
        print("\n" + "="*40)
        print("创建可视化图表")
        print("="*40)

        # GO气泡图
        for go_type in ['BP', 'CC', 'MF']:
            if go_type in up_go_results and not up_go_results[go_type].empty:
                self.create_bubble_plot(up_go_results[go_type],
                                      f"上调蛋白 GO-{go_type} 富集分析",
                                      f"up_proteins_GO_{go_type}")

            if go_type in down_go_results and not down_go_results[go_type].empty:
                self.create_bubble_plot(down_go_results[go_type],
                                      f"下调蛋白 GO-{go_type} 富集分析",
                                      f"down_proteins_GO_{go_type}")

        # KEGG气泡图
        if not up_kegg_results.empty:
            self.create_bubble_plot(up_kegg_results, "上调蛋白 KEGG通路富集分析",
                                  "up_proteins_KEGG", "KEGG")

        if not down_kegg_results.empty:
            self.create_bubble_plot(down_kegg_results, "下调蛋白 KEGG通路富集分析",
                                  "down_proteins_KEGG", "KEGG")

        # 6. 创建网络图
        print("\n" + "="*40)
        print("创建网络互作图")
        print("="*40)

        # BP网络图
        if 'BP' in up_go_results and not up_go_results['BP'].empty:
            self.create_network_plot(up_go_results['BP'], "上调蛋白 BP功能网络",
                                   "up_proteins_BP_network")

        if 'BP' in down_go_results and not down_go_results['BP'].empty:
            self.create_network_plot(down_go_results['BP'], "下调蛋白 BP功能网络",
                                   "down_proteins_BP_network")

        # KEGG网络图
        if not up_kegg_results.empty:
            self.create_network_plot(up_kegg_results, "上调蛋白 KEGG通路网络",
                                   "up_proteins_KEGG_network", "KEGG")

        if not down_kegg_results.empty:
            self.create_network_plot(down_kegg_results, "下调蛋白 KEGG通路网络",
                                   "down_proteins_KEGG_network", "KEGG")

        # 7. 生成分析报告
        self.generate_analysis_report(up_go_results, down_go_results,
                                    up_kegg_results, down_kegg_results)

        print("\n" + "="*60)
        print("分析完成！")
        print(f"所有结果已保存到: {self.output_dir.absolute()}")
        print("="*60)

    def generate_analysis_report(self, up_go_results, down_go_results,
                               up_kegg_results, down_kegg_results):
        """生成分析报告"""
        print("正在生成分析报告...")

        report_file = self.output_dir / "蛋白组差异分析报告.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 蛋白组差异分析报告\n\n")
            f.write(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 数据概况
            f.write("## 1. 数据概况\n\n")
            f.write(f"- 总蛋白数: {len(self.df)}\n")
            f.write(f"- 差异蛋白数: {len(self.diff_proteins)}\n")
            f.write(f"- 上调蛋白数: {len(self.up_proteins)}\n")
            f.write(f"- 下调蛋白数: {len(self.down_proteins)}\n\n")

            # GO富集分析结果
            f.write("## 2. GO功能富集分析\n\n")

            f.write("### 2.1 上调蛋白GO富集\n\n")
            for go_type in ['BP', 'CC', 'MF']:
                if go_type in up_go_results and not up_go_results[go_type].empty:
                    f.write(f"#### {go_type} (前10个条目)\n\n")
                    f.write("| 功能条目 | 蛋白数量 |\n")
                    f.write("|---------|----------|\n")
                    for term, count in up_go_results[go_type].head(10).items():
                        f.write(f"| {term[:50]}... | {count} |\n")
                    f.write("\n")

            f.write("### 2.2 下调蛋白GO富集\n\n")
            for go_type in ['BP', 'CC', 'MF']:
                if go_type in down_go_results and not down_go_results[go_type].empty:
                    f.write(f"#### {go_type} (前10个条目)\n\n")
                    f.write("| 功能条目 | 蛋白数量 |\n")
                    f.write("|---------|----------|\n")
                    for term, count in down_go_results[go_type].head(10).items():
                        f.write(f"| {term[:50]}... | {count} |\n")
                    f.write("\n")

            # KEGG富集分析结果
            f.write("## 3. KEGG通路富集分析\n\n")

            f.write("### 3.1 上调蛋白KEGG富集 (前10个通路)\n\n")
            if not up_kegg_results.empty:
                f.write("| 通路名称 | 蛋白数量 |\n")
                f.write("|---------|----------|\n")
                for pathway, count in up_kegg_results.head(10).items():
                    f.write(f"| {pathway} | {count} |\n")
            else:
                f.write("暂无KEGG富集结果\n")
            f.write("\n")

            f.write("### 3.2 下调蛋白KEGG富集 (前10个通路)\n\n")
            if not down_kegg_results.empty:
                f.write("| 通路名称 | 蛋白数量 |\n")
                f.write("|---------|----------|\n")
                for pathway, count in down_kegg_results.head(10).items():
                    f.write(f"| {pathway} | {count} |\n")
            else:
                f.write("暂无KEGG富集结果\n")
            f.write("\n")

            # 生物学意义解读
            f.write("## 4. 生物学意义解读\n\n")
            f.write("### 4.1 主要发现\n\n")
            f.write("1. **差异蛋白分布**: ")
            if len(self.up_proteins) > len(self.down_proteins):
                f.write("上调蛋白数量多于下调蛋白，提示HFsnEF组相比HFnEF组整体蛋白表达水平上升。\n\n")
            elif len(self.up_proteins) < len(self.down_proteins):
                f.write("下调蛋白数量多于上调蛋白，提示HFsnEF组相比HFnEF组整体蛋白表达水平下降。\n\n")
            else:
                f.write("上调和下调蛋白数量相当，提示两组间蛋白表达变化较为平衡。\n\n")

            f.write("2. **功能富集特点**: 基于GO和KEGG富集分析结果，差异蛋白主要涉及...\n\n")
            f.write("3. **通路调控**: 关键信号通路的变化可能与疾病发生发展相关...\n\n")

            f.write("### 4.2 图表说明\n\n")
            f.write("- **气泡图**: 圆圈大小表示富集到的蛋白数量，颜色深浅表示统计显著性\n")
            f.write("- **网络图**: 展示功能条目或通路间的相互关系，节点大小表示蛋白数量\n\n")

            f.write("## 5. 文件说明\n\n")
            f.write("### 5.1 表格文件 (tables/)\n")
            f.write("- `differential_proteins.xlsx`: 差异蛋白详细列表\n")
            f.write("- `kegg_annotations.csv`: KEGG通路注释结果\n\n")

            f.write("### 5.2 图片文件 (figures/)\n")
            f.write("- `*_bubble_plot.png`: GO和KEGG富集分析气泡图\n\n")

            f.write("### 5.3 网络文件 (networks/)\n")
            f.write("- `*_network.png`: 功能和通路网络互作图\n")
            f.write("- `*_network_data.json`: 网络数据文件\n\n")

        print(f"分析报告已保存到: {report_file}")


def main():
    """主函数"""
    print("蛋白组差异分析工具")
    print("="*50)

    # 检查数据文件
    data_file = "Proteins_all_diff.csv"
    if not os.path.exists(data_file):
        print(f"错误: 找不到数据文件 {data_file}")
        print("请确保CSV文件在当前目录下")
        return

    try:
        # 创建分析器实例
        analyzer = ProteinAnalyzer(data_file)

        # 运行完整分析
        analyzer.run_complete_analysis()

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
