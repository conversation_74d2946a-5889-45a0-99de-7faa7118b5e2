#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPI网络分析脚本
分析下调蛋白质的蛋白质互作网络，识别Hub蛋白并进行功能注释

作者：AI Assistant
日期：2025-08-02
"""

import pandas as pd
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import seaborn as sns
import requests
import json
import time
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PPINetworkAnalyzer:
    """PPI网络分析器"""
    
    def __init__(self, data_file):
        """初始化分析器"""
        self.data_file = data_file
        self.df = None
        self.gene_list = []
        self.ppi_data = None
        self.G = nx.Graph()
        self.hub_proteins = []
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.df = pd.read_csv(self.data_file)
        print(f"数据加载完成，共{len(self.df)}个蛋白质")
        
        # 提取基因名
        gene_names = self.df['Gene Name'].dropna().unique()
        # 过滤掉空值和无效基因名
        self.gene_list = [gene for gene in gene_names if gene and gene != 'nan' and len(gene) > 1]
        print(f"提取到{len(self.gene_list)}个有效基因名")
        
    def query_string_api(self, genes, species=9606, confidence=0.4):
        """查询STRING数据库获取PPI数据"""
        print(f"正在查询STRING数据库，置信度阈值：{confidence}")
        
        # STRING API URL
        string_api_url = "https://string-db.org/api"
        output_format = "json"
        method = "network"
        
        # 构建请求参数
        params = {
            "identifiers": "%0d".join(genes),
            "species": species,
            "required_score": int(confidence * 1000),  # STRING使用0-1000的分数
            "add_white_nodes": 0,
            "caller_identity": "ppi_analysis"
        }
        
        # 发送请求
        request_url = f"{string_api_url}/{output_format}/{method}"
        
        try:
            response = requests.post(request_url, data=params)
            response.raise_for_status()
            
            # 解析JSON响应
            ppi_data = response.json()
            print(f"成功获取{len(ppi_data)}条互作关系")
            
            return ppi_data
            
        except requests.exceptions.RequestException as e:
            print(f"STRING API请求失败: {e}")
            return []
    
    def build_network(self, confidence=0.4):
        """构建PPI网络"""
        print("正在构建PPI网络...")
        
        # 分批查询（STRING API有限制）
        batch_size = 100
        all_ppi_data = []
        
        for i in range(0, len(self.gene_list), batch_size):
            batch_genes = self.gene_list[i:i+batch_size]
            print(f"查询第{i//batch_size + 1}批基因 ({len(batch_genes)}个)")
            
            batch_ppi = self.query_string_api(batch_genes, confidence=confidence)
            all_ppi_data.extend(batch_ppi)
            
            # 避免请求过于频繁
            time.sleep(1)
        
        self.ppi_data = all_ppi_data
        
        # 构建NetworkX图
        self.G = nx.Graph()
        
        for interaction in self.ppi_data:
            protein1 = interaction['preferredName_A']
            protein2 = interaction['preferredName_B']
            score = float(interaction['score'])
            
            # 添加边，权重为置信度分数
            self.G.add_edge(protein1, protein2, weight=score)
        
        print(f"网络构建完成：{self.G.number_of_nodes()}个节点，{self.G.number_of_edges()}条边")
        
    def calculate_network_properties(self):
        """计算网络拓扑属性"""
        print("正在计算网络拓扑属性...")
        
        if self.G.number_of_nodes() == 0:
            print("网络为空，无法计算属性")
            return {}
        
        # 基本网络属性
        properties = {
            'nodes': self.G.number_of_nodes(),
            'edges': self.G.number_of_edges(),
            'density': nx.density(self.G),
            'connected_components': nx.number_connected_components(self.G)
        }
        
        # 如果网络连通，计算更多属性
        if nx.is_connected(self.G):
            properties['diameter'] = nx.diameter(self.G)
            properties['average_path_length'] = nx.average_shortest_path_length(self.G)
        else:
            # 对最大连通分量计算
            largest_cc = max(nx.connected_components(self.G), key=len)
            subgraph = self.G.subgraph(largest_cc)
            properties['largest_cc_size'] = len(largest_cc)
            properties['diameter'] = nx.diameter(subgraph)
            properties['average_path_length'] = nx.average_shortest_path_length(subgraph)
        
        # 计算中心性指标
        degree_centrality = nx.degree_centrality(self.G)
        betweenness_centrality = nx.betweenness_centrality(self.G)
        closeness_centrality = nx.closeness_centrality(self.G)
        
        # 创建节点属性DataFrame
        node_properties = pd.DataFrame({
            'protein': list(self.G.nodes()),
            'degree': [self.G.degree(node) for node in self.G.nodes()],
            'degree_centrality': [degree_centrality[node] for node in self.G.nodes()],
            'betweenness_centrality': [betweenness_centrality[node] for node in self.G.nodes()],
            'closeness_centrality': [closeness_centrality[node] for node in self.G.nodes()]
        })
        
        # 按度中心性排序，识别Hub蛋白
        node_properties = node_properties.sort_values('degree', ascending=False)
        self.hub_proteins = node_properties.head(10)
        
        print("网络属性计算完成")
        print(f"网络密度: {properties['density']:.4f}")
        print(f"连通分量数: {properties['connected_components']}")
        
        return properties, node_properties
    
    def visualize_network(self, output_file='ppi_network.png', top_n=50):
        """可视化PPI网络"""
        print(f"正在生成网络可视化图（显示前{top_n}个节点）...")
        
        if self.G.number_of_nodes() == 0:
            print("网络为空，无法可视化")
            return
        
        # 如果节点太多，只显示度最高的节点
        if self.G.number_of_nodes() > top_n:
            degrees = dict(self.G.degree())
            top_nodes = sorted(degrees.items(), key=lambda x: x[1], reverse=True)[:top_n]
            subgraph = self.G.subgraph([node for node, _ in top_nodes])
        else:
            subgraph = self.G
        
        plt.figure(figsize=(15, 12))
        
        # 计算布局
        pos = nx.spring_layout(subgraph, k=1, iterations=50)
        
        # 节点大小基于度
        node_sizes = [subgraph.degree(node) * 50 + 100 for node in subgraph.nodes()]
        
        # 绘制网络
        nx.draw_networkx_nodes(subgraph, pos, node_size=node_sizes, 
                              node_color='lightblue', alpha=0.7)
        nx.draw_networkx_edges(subgraph, pos, alpha=0.3, width=0.5)
        nx.draw_networkx_labels(subgraph, pos, font_size=8, font_weight='bold')
        
        plt.title(f'蛋白质互作网络 (前{len(subgraph.nodes())}个节点)', fontsize=16, fontweight='bold')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"网络图已保存至: {output_file}")
    
    def save_results(self, properties, node_properties):
        """保存分析结果"""
        print("正在保存分析结果...")
        
        # 保存网络属性
        with open('network_properties.json', 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)
        
        # 保存节点属性
        node_properties.to_csv('node_properties.csv', index=False, encoding='utf-8-sig')
        
        # 保存Hub蛋白
        self.hub_proteins.to_csv('hub_proteins.csv', index=False, encoding='utf-8-sig')
        
        # 保存PPI数据
        if self.ppi_data:
            ppi_df = pd.DataFrame(self.ppi_data)
            ppi_df.to_csv('ppi_interactions.csv', index=False, encoding='utf-8-sig')
        
        print("结果保存完成")
        
        # 打印Hub蛋白信息
        print("\n=== 前10个Hub蛋白 ===")
        for idx, row in self.hub_proteins.iterrows():
            print(f"{row['protein']}: 度={row['degree']}, 度中心性={row['degree_centrality']:.4f}")

def main():
    """主函数"""
    print("=== PPI网络分析开始 ===")
    
    # 初始化分析器
    analyzer = PPINetworkAnalyzer('final_enrichment_results_down.csv')
    
    # 执行分析流程
    analyzer.load_data()
    analyzer.build_network(confidence=0.4)
    properties, node_properties = analyzer.calculate_network_properties()
    analyzer.visualize_network()
    analyzer.save_results(properties, node_properties)
    
    print("\n=== PPI网络分析完成 ===")

if __name__ == "__main__":
    main()
