# 下调蛋白质PPI网络分析系统

## 概述

本系统用于分析显著下调蛋白质的蛋白质互作网络(PPI)，识别Hub蛋白并进行功能注释。分析流程包括三个主要步骤：

1. **PPI网络构建与拓扑分析** - 使用STRING数据库构建互作网络
2. **Hub蛋白识别** - 基于网络拓扑参数识别关键蛋白质
3. **功能注释** - 对Hub蛋白进行GO和KEGG富集分析

## 文件结构

```
ppi_analysis/
├── main_analysis.py              # 主分析脚本
├── ppi_network_analysis.py       # PPI网络分析模块
├── functional_annotation.py      # 功能注释分析模块
├── requirements.txt              # 依赖包列表
├── README.md                     # 说明文档
└── ppi_analysis_results/         # 输出结果目录（运行后生成）
    ├── network_properties.json   # 网络拓扑属性
    ├── node_properties.csv       # 所有节点属性
    ├── hub_proteins.csv          # Hub蛋白列表
    ├── ppi_interactions.csv      # PPI互作数据
    ├── ppi_network.png           # PPI网络可视化图
    ├── enrichment_analysis.png   # 富集分析图
    ├── *_enrichment.csv          # 各类富集分析结果
    ├── functional_annotation_report.md  # 功能注释报告
    └── comprehensive_analysis_report.md # 综合分析报告
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：运行完整分析流程（推荐）

```bash
python main_analysis.py
```

这将自动执行所有三个分析步骤并生成综合报告。

### 方法2：分步运行

如果需要分步执行或调试，可以单独运行各个模块：

```bash
# 第一步：PPI网络分析
python ppi_network_analysis.py

# 第二步：功能注释分析（需要先完成第一步）
python functional_annotation.py
```

## 输入数据要求

- 输入文件：`final_enrichment_results_down.csv`
- 必需字段：`Gene Name` - 包含基因名称
- 文件格式：CSV格式，UTF-8编码

## 输出结果说明

### 1. 网络分析结果

- **network_properties.json**: 网络的基本拓扑属性
- **node_properties.csv**: 每个节点的中心性指标
- **hub_proteins.csv**: 前10个Hub蛋白及其属性
- **ppi_interactions.csv**: 所有蛋白质互作关系
- **ppi_network.png**: 网络可视化图

### 2. 功能注释结果

- **go_*_enrichment.csv**: GO富集分析结果（BP/MF/CC）
- **kegg_*_enrichment.csv**: KEGG通路富集分析结果
- **enrichment_analysis.png**: 富集分析可视化图
- **functional_annotation_report.md**: 功能注释详细报告

### 3. 综合报告

- **comprehensive_analysis_report.md**: 包含所有分析结果的综合报告

## 参数配置

### PPI网络构建参数

在`ppi_network_analysis.py`中可以调整：

- `confidence`: STRING数据库置信度阈值（默认0.4）
- `species`: 物种ID（默认9606为人类）
- `top_n`: 网络可视化显示的节点数（默认50）

### 富集分析参数

在`functional_annotation.py`中可以调整：

- 显著性阈值：Adjusted P-value < 0.05
- 结果数量：每个数据库返回前20个结果

## 注意事项

1. **网络连接**: 需要稳定的网络连接访问STRING和Enrichr API
2. **API限制**: 为避免请求过于频繁，脚本中加入了延时
3. **数据质量**: 确保输入数据中的基因名称准确无误
4. **内存使用**: 大型网络可能需要较多内存

## 故障排除

### 常见问题

1. **网络请求失败**
   - 检查网络连接
   - 确认API服务可用
   - 适当增加请求间隔时间

2. **基因名称无法识别**
   - 检查基因名称格式
   - 确认使用官方基因符号
   - 考虑进行基因名称标准化

3. **内存不足**
   - 减少分析的基因数量
   - 增加系统内存
   - 使用更高效的数据结构

### 调试模式

在脚本中设置详细的日志输出来诊断问题：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 技术支持

如遇到问题，请检查：

1. Python版本（推荐3.7+）
2. 依赖包版本
3. 输入数据格式
4. 网络连接状态

## 更新日志

- v1.0 (2025-08-02): 初始版本，包含完整的PPI网络分析流程
