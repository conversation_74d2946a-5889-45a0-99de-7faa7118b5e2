#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PLS-DA模型验证分析
包含置换检验、小提琴图和ROC曲线分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cross_decomposition import PLSRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import roc_curve, auc, roc_auc_score
from sklearn.linear_model import LogisticRegression
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PLSDAValidator:
    def __init__(self, data_file):
        """初始化分析器"""
        self.data_file = data_file
        self.data = None
        self.diff_proteins = None
        self.expression_data = None
        self.group_labels = None
        self.top10_proteins = None
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.data_file)
        
        # 筛选差异蛋白
        fc_col = 'FC(HFsnEF_vs_HFnEF)'
        pvalue_col = 'pValue(HFsnEF_vs_HFnEF)'
        
        pvalue_filter = self.data[pvalue_col] < 0.05
        fc_up_filter = self.data[fc_col] > 1.5
        fc_down_filter = self.data[fc_col] < (1/1.5)
        fc_filter = fc_up_filter | fc_down_filter
        
        self.diff_proteins = self.data[pvalue_filter & fc_filter].copy()
        
        # 获取前10个最显著的差异蛋白
        self.top10_proteins = self.diff_proteins.nsmallest(10, pvalue_col)
        
        # 识别样本列
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        
        # 准备表达数据
        all_samples = hfnef_samples + hfsnef_samples
        expression_matrix = self.diff_proteins[all_samples].values
        
        # 处理数据
        expression_matrix = np.where(expression_matrix <= 0.001, np.nan, expression_matrix)
        expression_matrix = np.log2(expression_matrix + 1e-6)
        
        # 填充缺失值
        for i in range(expression_matrix.shape[0]):
            row = expression_matrix[i, :]
            if np.any(np.isnan(row)):
                row_mean = np.nanmean(row)
                row[np.isnan(row)] = row_mean
        
        self.expression_data = expression_matrix.T
        self.group_labels = np.array([0] * len(hfnef_samples) + [1] * len(hfsnef_samples))
        
        print(f"数据准备完成: {len(self.diff_proteins)}个差异蛋白, {len(all_samples)}个样本")
        
    def permutation_test(self, n_permutations=100, n_components=2):
        """执行置换检验"""
        print(f"\n正在进行置换检验 (n_permutations={n_permutations})...")
        
        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(self.expression_data)
        y = self.group_labels
        
        # 计算真实的R2和Q2
        pls = PLSRegression(n_components=n_components, scale=False)
        pls.fit(X_scaled, y)
        
        # R2 (拟合优度)
        y_pred = pls.predict(X_scaled)
        real_r2 = 1 - np.sum((y.reshape(-1, 1) - y_pred)**2) / np.sum((y.reshape(-1, 1) - np.mean(y))**2)
        
        # Q2 (交叉验证)
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        real_q2 = np.mean(cross_val_score(pls, X_scaled, y, cv=cv, scoring='r2'))
        
        print(f"真实 R2: {real_r2:.3f}")
        print(f"真实 Q2: {real_q2:.3f}")
        
        # 置换检验
        perm_r2_scores = []
        perm_q2_scores = []
        
        for i in range(n_permutations):
            if (i + 1) % 20 == 0:
                print(f"置换进度: {i+1}/{n_permutations}")
            
            # 随机打乱标签
            y_perm = np.random.permutation(y)
            
            # 计算置换后的R2和Q2
            pls_perm = PLSRegression(n_components=n_components, scale=False)
            pls_perm.fit(X_scaled, y_perm)
            
            # R2
            y_pred_perm = pls_perm.predict(X_scaled)
            r2_perm = 1 - np.sum((y_perm.reshape(-1, 1) - y_pred_perm)**2) / np.sum((y_perm.reshape(-1, 1) - np.mean(y_perm))**2)
            perm_r2_scores.append(r2_perm)
            
            # Q2
            q2_perm = np.mean(cross_val_score(pls_perm, X_scaled, y_perm, cv=cv, scoring='r2'))
            perm_q2_scores.append(q2_perm)
        
        # 计算p值
        p_r2 = np.sum(np.array(perm_r2_scores) >= real_r2) / n_permutations
        p_q2 = np.sum(np.array(perm_q2_scores) >= real_q2) / n_permutations
        
        print(f"R2 p值: {p_r2:.3f}")
        print(f"Q2 p值: {p_q2:.3f}")
        
        # 绘制置换检验结果
        self.plot_permutation_results(perm_r2_scores, perm_q2_scores, real_r2, real_q2, p_r2, p_q2)
        
        return real_r2, real_q2, p_r2, p_q2, perm_r2_scores, perm_q2_scores
    
    def plot_permutation_results(self, perm_r2, perm_q2, real_r2, real_q2, p_r2, p_q2):
        """绘制置换检验结果"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        
        # 绘制R2置换分布
        ax.hist(perm_r2, bins=30, alpha=0.7, color='orange', label=f'Perm R2Y', density=True)
        ax.axvline(real_r2, color='orange', linestyle='--', linewidth=2, 
                  label=f'R2Y: {real_r2:.3f}\np = {p_r2:.3f} ({int(p_r2*100)}/100)')
        
        # 绘制Q2置换分布
        ax.hist(perm_q2, bins=30, alpha=0.7, color='steelblue', label=f'Perm Q2', density=True)
        ax.axvline(real_q2, color='steelblue', linestyle='--', linewidth=2,
                  label=f'Q2: {real_q2:.3f}\np = {p_q2:.3f} ({int(p_q2*100)}/100)')
        
        ax.set_xlabel('Permutations', fontsize=12)
        ax.set_ylabel('Frequency', fontsize=12)
        ax.set_title('PLS-DA置换检验结果', fontsize=14, fontweight='bold')
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('PLSDA_置换检验结果.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def create_violin_plots(self):
        """创建前10个差异蛋白的小提琴图"""
        print("\n正在创建小提琴图...")
        
        # 获取前10个蛋白的表达数据
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        
        # 准备绘图数据
        plot_data = []
        for _, protein in self.top10_proteins.iterrows():
            protein_id = protein['Gene Name']
            
            # HFnEF组数据
            hfnef_values = protein[hfnef_samples].values.astype(float)
            hfnef_values = np.log2(hfnef_values + 1e-6)
            for val in hfnef_values:
                if not np.isnan(val) and val > -10:  # 过滤异常值
                    plot_data.append({
                        'Protein': protein_id,
                        'Expression': val,
                        'Group': 'HFnEF'
                    })

            # HFsnEF组数据
            hfsnef_values = protein[hfsnef_samples].values.astype(float)
            hfsnef_values = np.log2(hfsnef_values + 1e-6)
            for val in hfsnef_values:
                if not np.isnan(val) and val > -10:  # 过滤异常值
                    plot_data.append({
                        'Protein': protein_id,
                        'Expression': val,
                        'Group': 'HFsnEF'
                    })
        
        df_plot = pd.DataFrame(plot_data)
        
        # 创建小提琴图
        plt.figure(figsize=(15, 8))
        
        # 使用seaborn绘制小提琴图
        ax = sns.violinplot(data=df_plot, x='Protein', y='Expression', hue='Group', 
                           palette=['steelblue', 'orange'], split=False, inner='quart')
        
        plt.xticks(rotation=45, ha='right')
        plt.xlabel('蛋白名称', fontsize=12)
        plt.ylabel('Log2(表达量)', fontsize=12)
        plt.title('前10个最显著差异蛋白表达分布', fontsize=14, fontweight='bold')
        plt.legend(title='分组', fontsize=11)
        plt.grid(True, alpha=0.3, axis='y')
        
        plt.tight_layout()
        plt.savefig('前10个差异蛋白小提琴图.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return df_plot

    def create_roc_curves(self):
        """创建ROC曲线分析"""
        print("\n正在创建ROC曲线...")

        # 获取前10个蛋白的表达数据
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        all_samples = hfnef_samples + hfsnef_samples

        # 准备数据
        top10_expression = self.top10_proteins[all_samples].values.astype(float)
        top10_expression = np.log2(top10_expression + 1e-6)

        # 填充缺失值
        for i in range(top10_expression.shape[0]):
            row = top10_expression[i, :]
            if np.any(np.isnan(row)):
                row_mean = np.nanmean(row)
                row[np.isnan(row)] = row_mean

        top10_expression = top10_expression.T  # 转置，样本为行
        y_true = self.group_labels

        # 创建ROC曲线图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('ROC曲线分析', fontsize=16, fontweight='bold')

        # 1. 单个蛋白ROC曲线 (前5个)
        ax1 = axes[0, 0]
        colors = plt.cm.Set3(np.linspace(0, 1, 5))

        for i in range(5):
            protein_name = self.top10_proteins.iloc[i]['Gene Name']
            X_single = top10_expression[:, i].reshape(-1, 1)

            # 使用逻辑回归
            lr = LogisticRegression(random_state=42)
            lr.fit(X_single, y_true)
            y_prob = lr.predict_proba(X_single)[:, 1]

            fpr, tpr, _ = roc_curve(y_true, y_prob)
            auc_score = auc(fpr, tpr)

            ax1.plot(fpr, tpr, color=colors[i], linewidth=2,
                    label=f'{protein_name} (AUC={auc_score:.3f})')

        ax1.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax1.set_xlabel('假阳性率 (1-特异性)')
        ax1.set_ylabel('真阳性率 (敏感性)')
        ax1.set_title('单个蛋白ROC曲线 (前5个)')
        ax1.legend(fontsize=9)
        ax1.grid(True, alpha=0.3)

        # 2. 单个蛋白ROC曲线 (后5个)
        ax2 = axes[0, 1]
        colors = plt.cm.Set3(np.linspace(0, 1, 5))

        for i in range(5, 10):
            protein_name = self.top10_proteins.iloc[i]['Gene Name']
            X_single = top10_expression[:, i].reshape(-1, 1)

            lr = LogisticRegression(random_state=42)
            lr.fit(X_single, y_true)
            y_prob = lr.predict_proba(X_single)[:, 1]

            fpr, tpr, _ = roc_curve(y_true, y_prob)
            auc_score = auc(fpr, tpr)

            ax2.plot(fpr, tpr, color=colors[i-5], linewidth=2,
                    label=f'{protein_name} (AUC={auc_score:.3f})')

        ax2.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax2.set_xlabel('假阳性率 (1-特异性)')
        ax2.set_ylabel('真阳性率 (敏感性)')
        ax2.set_title('单个蛋白ROC曲线 (后5个)')
        ax2.legend(fontsize=9)
        ax2.grid(True, alpha=0.3)

        # 3. 联合模型ROC曲线
        ax3 = axes[1, 0]

        # 使用不同数量的蛋白构建联合模型
        protein_counts = [1, 3, 5, 10]
        colors = ['red', 'blue', 'green', 'purple']

        for i, n_proteins in enumerate(protein_counts):
            X_combined = top10_expression[:, :n_proteins]

            lr = LogisticRegression(random_state=42, max_iter=1000)
            lr.fit(X_combined, y_true)
            y_prob = lr.predict_proba(X_combined)[:, 1]

            fpr, tpr, _ = roc_curve(y_true, y_prob)
            auc_score = auc(fpr, tpr)

            ax3.plot(fpr, tpr, color=colors[i], linewidth=2,
                    label=f'前{n_proteins}个蛋白 (AUC={auc_score:.3f})')

        ax3.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax3.set_xlabel('假阳性率 (1-特异性)')
        ax3.set_ylabel('真阳性率 (敏感性)')
        ax3.set_title('联合蛋白ROC曲线')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. AUC比较柱状图
        ax4 = axes[1, 1]

        # 计算所有单个蛋白的AUC
        single_aucs = []
        protein_names = []

        for i in range(10):
            protein_name = self.top10_proteins.iloc[i]['Gene Name']
            X_single = top10_expression[:, i].reshape(-1, 1)

            lr = LogisticRegression(random_state=42)
            lr.fit(X_single, y_true)
            y_prob = lr.predict_proba(X_single)[:, 1]

            auc_score = roc_auc_score(y_true, y_prob)
            single_aucs.append(auc_score)
            protein_names.append(protein_name)

        # 计算联合模型AUC
        X_combined = top10_expression
        lr = LogisticRegression(random_state=42, max_iter=1000)
        lr.fit(X_combined, y_true)
        y_prob = lr.predict_proba(X_combined)[:, 1]
        combined_auc = roc_auc_score(y_true, y_prob)

        # 绘制柱状图
        x_pos = np.arange(len(protein_names))
        bars = ax4.bar(x_pos, single_aucs, alpha=0.7, color='steelblue', label='单个蛋白')

        # 添加联合模型线
        ax4.axhline(y=combined_auc, color='red', linestyle='--', linewidth=2,
                   label=f'10个蛋白联合 (AUC={combined_auc:.3f})')

        ax4.set_xlabel('蛋白名称')
        ax4.set_ylabel('AUC值')
        ax4.set_title('AUC值比较')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels(protein_names, rotation=45, ha='right')
        ax4.legend()
        ax4.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, auc_val in zip(bars, single_aucs):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{auc_val:.3f}', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig('ROC曲线分析.png', dpi=300, bbox_inches='tight')
        plt.show()

        return single_aucs, combined_auc

    def generate_summary_report(self, real_r2, real_q2, p_r2, p_q2, single_aucs, combined_auc):
        """生成分析结果汇总报告"""
        print("\n" + "="*60)
        print("PLS-DA模型验证分析结果汇总")
        print("="*60)

        # 置换检验结果
        print(f"\n1. 置换检验结果:")
        print(f"   R2Y: {real_r2:.3f} (p = {p_r2:.3f})")
        print(f"   Q2: {real_q2:.3f} (p = {p_q2:.3f})")

        if p_r2 < 0.05:
            print("   ✓ R2Y显著 (p < 0.05)")
        else:
            print("   ✗ R2Y不显著 (p ≥ 0.05)")

        if p_q2 < 0.05:
            print("   ✓ Q2显著 (p < 0.05)")
        else:
            print("   ✗ Q2不显著 (p ≥ 0.05)")

        # 前10个差异蛋白信息
        print(f"\n2. 前10个最显著差异蛋白:")
        for i, (_, protein) in enumerate(self.top10_proteins.iterrows()):
            gene_name = protein['Gene Name']
            fc = protein['FC(HFsnEF_vs_HFnEF)']
            pval = protein['pValue(HFsnEF_vs_HFnEF)']
            regulation = "上调" if fc > 1 else "下调"
            print(f"   {i+1:2d}. {gene_name:12} | FC: {fc:6.3f} | p: {pval:.2e} | {regulation}")

        # ROC分析结果
        print(f"\n3. ROC曲线分析结果:")
        print(f"   单个蛋白AUC范围: {min(single_aucs):.3f} - {max(single_aucs):.3f}")
        print(f"   平均单个蛋白AUC: {np.mean(single_aucs):.3f}")
        print(f"   10个蛋白联合AUC: {combined_auc:.3f}")

        # 最佳单个蛋白
        best_protein_idx = np.argmax(single_aucs)
        best_protein_name = self.top10_proteins.iloc[best_protein_idx]['Gene Name']
        best_auc = single_aucs[best_protein_idx]
        print(f"   最佳单个蛋白: {best_protein_name} (AUC = {best_auc:.3f})")

        # 模型性能评价
        print(f"\n4. 模型性能评价:")
        if combined_auc >= 0.9:
            print("   联合模型分类性能: 优秀 (AUC ≥ 0.9)")
        elif combined_auc >= 0.8:
            print("   联合模型分类性能: 良好 (AUC ≥ 0.8)")
        elif combined_auc >= 0.7:
            print("   联合模型分类性能: 一般 (AUC ≥ 0.7)")
        else:
            print("   联合模型分类性能: 较差 (AUC < 0.7)")

        improvement = combined_auc - np.mean(single_aucs)
        print(f"   联合模型相比单个蛋白平均提升: {improvement:.3f}")

        print(f"\n5. 输出文件:")
        print("   PLSDA_置换检验结果.png - 置换检验柱状图")
        print("   前10个差异蛋白小提琴图.png - 表达分布小提琴图")
        print("   ROC曲线分析.png - ROC曲线和AUC比较")

        print("\n分析完成！")
        print("="*60)

def main():
    """主函数"""
    print("开始PLS-DA模型验证分析...")

    # 初始化分析器
    validator = PLSDAValidator('Proteins_all_diff.csv')

    try:
        # 1. 加载和准备数据
        validator.load_and_prepare_data()

        # 2. 置换检验
        real_r2, real_q2, p_r2, p_q2, perm_r2, perm_q2 = validator.permutation_test(n_permutations=100)

        # 3. 创建小提琴图
        df_plot = validator.create_violin_plots()

        # 4. 创建ROC曲线
        single_aucs, combined_auc = validator.create_roc_curves()

        # 5. 生成汇总报告
        validator.generate_summary_report(real_r2, real_q2, p_r2, p_q2, single_aucs, combined_auc)

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
