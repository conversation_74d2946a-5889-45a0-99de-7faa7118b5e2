#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能注释分析脚本
对Hub蛋白进行GO和KEGG富集分析

作者：AI Assistant
日期：2025-08-02
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import requests
import json
import time
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class FunctionalAnnotator:
    """功能注释分析器"""
    
    def __init__(self, hub_proteins_file='hub_proteins.csv'):
        """初始化注释器"""
        self.hub_proteins_file = hub_proteins_file
        self.hub_proteins = []
        self.go_results = {}
        self.kegg_results = {}
        
    def load_hub_proteins(self):
        """加载Hub蛋白数据"""
        print("正在加载Hub蛋白数据...")
        try:
            df = pd.read_csv(self.hub_proteins_file)
            self.hub_proteins = df['protein'].tolist()
            print(f"加载了{len(self.hub_proteins)}个Hub蛋白")
            print("Hub蛋白列表:", ', '.join(self.hub_proteins))
        except FileNotFoundError:
            print(f"未找到文件: {self.hub_proteins_file}")
            return False
        return True
    
    def query_enrichr_api(self, genes, gene_set_library):
        """查询Enrichr API进行富集分析"""
        print(f"正在查询{gene_set_library}数据库...")
        
        # Enrichr API URLs
        ENRICHR_URL = 'https://maayanlab.cloud/Enrichr/addList'
        ENRICHR_QUERY_URL = 'https://maayanlab.cloud/Enrichr/enrich'
        
        # 提交基因列表
        genes_str = '\n'.join(genes)
        payload = {
            'list': genes_str,
            'description': 'Hub proteins from PPI analysis'
        }
        
        try:
            response = requests.post(ENRICHR_URL, files=payload)
            response.raise_for_status()
            
            data = response.json()
            user_list_id = data['userListId']
            
            # 查询富集结果
            query_string = f'?userListId={user_list_id}&backgroundType={gene_set_library}'
            response = requests.get(ENRICHR_QUERY_URL + query_string)
            response.raise_for_status()
            
            enrichment_results = response.json()
            
            if gene_set_library in enrichment_results:
                results = enrichment_results[gene_set_library]
                print(f"获取到{len(results)}个{gene_set_library}富集结果")
                return results
            else:
                print(f"未找到{gene_set_library}的富集结果")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"Enrichr API请求失败: {e}")
            return []
        except Exception as e:
            print(f"处理富集结果时出错: {e}")
            return []
    
    def perform_go_enrichment(self):
        """执行GO富集分析"""
        print("=== 开始GO富集分析 ===")
        
        go_databases = [
            'GO_Biological_Process_2023',
            'GO_Molecular_Function_2023', 
            'GO_Cellular_Component_2023'
        ]
        
        self.go_results = {}
        
        for db in go_databases:
            print(f"\n分析{db}...")
            results = self.query_enrichr_api(self.hub_proteins, db)
            
            if results:
                # 转换为DataFrame并筛选显著结果
                df = pd.DataFrame(results, columns=[
                    'Term', 'Overlap', 'P-value', 'Adjusted P-value', 
                    'Old P-value', 'Old Adjusted P-value', 'Odds Ratio', 
                    'Combined Score', 'Genes'
                ])
                
                # 筛选显著结果 (Adjusted P-value < 0.05)
                significant = df[df['Adjusted P-value'] < 0.05].head(20)
                self.go_results[db] = significant
                
                print(f"找到{len(significant)}个显著富集的GO条目")
            
            time.sleep(1)  # 避免请求过于频繁
    
    def perform_kegg_enrichment(self):
        """执行KEGG通路富集分析"""
        print("\n=== 开始KEGG通路富集分析 ===")
        
        kegg_databases = [
            'KEGG_2021_Human',
            'WikiPathway_2023_Human'
        ]
        
        self.kegg_results = {}
        
        for db in kegg_databases:
            print(f"\n分析{db}...")
            results = self.query_enrichr_api(self.hub_proteins, db)
            
            if results:
                # 转换为DataFrame并筛选显著结果
                df = pd.DataFrame(results, columns=[
                    'Term', 'Overlap', 'P-value', 'Adjusted P-value', 
                    'Old P-value', 'Old Adjusted P-value', 'Odds Ratio', 
                    'Combined Score', 'Genes'
                ])
                
                # 筛选显著结果
                significant = df[df['Adjusted P-value'] < 0.05].head(20)
                self.kegg_results[db] = significant
                
                print(f"找到{len(significant)}个显著富集的通路")
            
            time.sleep(1)
    
    def visualize_enrichment_results(self):
        """可视化富集分析结果"""
        print("\n正在生成富集分析可视化图...")
        
        # GO富集结果可视化
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        axes = axes.flatten()
        
        plot_idx = 0
        
        for db_name, results in self.go_results.items():
            if not results.empty and plot_idx < 3:
                ax = axes[plot_idx]
                
                # 取前10个最显著的结果
                top_results = results.head(10)
                
                # 创建条形图
                y_pos = np.arange(len(top_results))
                bars = ax.barh(y_pos, -np.log10(top_results['Adjusted P-value']), 
                              color=plt.cm.Set3(plot_idx))
                
                # 设置标签
                ax.set_yticks(y_pos)
                ax.set_yticklabels([term.split('(')[0][:50] + '...' if len(term) > 50 
                                   else term.split('(')[0] for term in top_results['Term']], 
                                  fontsize=10)
                ax.set_xlabel('-log10(Adjusted P-value)', fontsize=12)
                ax.set_title(db_name.replace('_', ' '), fontsize=14, fontweight='bold')
                
                # 添加数值标签
                for i, bar in enumerate(bars):
                    width = bar.get_width()
                    ax.text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                           f'{width:.2f}', ha='left', va='center', fontsize=9)
                
                plot_idx += 1
        
        # KEGG结果可视化
        if self.kegg_results:
            for db_name, results in self.kegg_results.items():
                if not results.empty and plot_idx < 4:
                    ax = axes[plot_idx]
                    
                    top_results = results.head(10)
                    y_pos = np.arange(len(top_results))
                    bars = ax.barh(y_pos, -np.log10(top_results['Adjusted P-value']), 
                                  color=plt.cm.Set3(plot_idx))
                    
                    ax.set_yticks(y_pos)
                    ax.set_yticklabels([term[:50] + '...' if len(term) > 50 
                                       else term for term in top_results['Term']], 
                                      fontsize=10)
                    ax.set_xlabel('-log10(Adjusted P-value)', fontsize=12)
                    ax.set_title(db_name.replace('_', ' '), fontsize=14, fontweight='bold')
                    
                    for i, bar in enumerate(bars):
                        width = bar.get_width()
                        ax.text(width + 0.1, bar.get_y() + bar.get_height()/2, 
                               f'{width:.2f}', ha='left', va='center', fontsize=9)
                    
                    plot_idx += 1
        
        # 隐藏未使用的子图
        for i in range(plot_idx, 4):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig('enrichment_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("富集分析图已保存至: enrichment_analysis.png")
    
    def save_enrichment_results(self):
        """保存富集分析结果"""
        print("\n正在保存富集分析结果...")
        
        # 保存GO结果
        for db_name, results in self.go_results.items():
            if not results.empty:
                filename = f"{db_name.lower()}_enrichment.csv"
                results.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"GO结果已保存至: {filename}")
        
        # 保存KEGG结果
        for db_name, results in self.kegg_results.items():
            if not results.empty:
                filename = f"{db_name.lower()}_enrichment.csv"
                results.to_csv(filename, index=False, encoding='utf-8-sig')
                print(f"通路结果已保存至: {filename}")
    
    def generate_summary_report(self):
        """生成分析总结报告"""
        print("\n正在生成分析总结报告...")
        
        report = []
        report.append("# Hub蛋白功能注释分析报告\n")
        report.append(f"分析日期: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append(f"分析的Hub蛋白数量: {len(self.hub_proteins)}\n")
        report.append(f"Hub蛋白列表: {', '.join(self.hub_proteins)}\n\n")
        
        # GO富集结果总结
        report.append("## GO富集分析结果\n")
        for db_name, results in self.go_results.items():
            if not results.empty:
                report.append(f"### {db_name.replace('_', ' ')}\n")
                report.append(f"显著富集条目数: {len(results)}\n")
                report.append("前5个最显著的条目:\n")
                for idx, row in results.head(5).iterrows():
                    report.append(f"- {row['Term']} (P-adj: {row['Adjusted P-value']:.2e})\n")
                report.append("\n")
        
        # KEGG富集结果总结
        report.append("## 通路富集分析结果\n")
        for db_name, results in self.kegg_results.items():
            if not results.empty:
                report.append(f"### {db_name.replace('_', ' ')}\n")
                report.append(f"显著富集通路数: {len(results)}\n")
                report.append("前5个最显著的通路:\n")
                for idx, row in results.head(5).iterrows():
                    report.append(f"- {row['Term']} (P-adj: {row['Adjusted P-value']:.2e})\n")
                report.append("\n")
        
        # 保存报告
        with open('functional_annotation_report.md', 'w', encoding='utf-8') as f:
            f.writelines(report)
        
        print("分析报告已保存至: functional_annotation_report.md")

def main():
    """主函数"""
    print("=== 功能注释分析开始 ===")
    
    # 初始化注释器
    annotator = FunctionalAnnotator()
    
    # 执行分析流程
    if annotator.load_hub_proteins():
        annotator.perform_go_enrichment()
        annotator.perform_kegg_enrichment()
        annotator.visualize_enrichment_results()
        annotator.save_enrichment_results()
        annotator.generate_summary_report()
        
        print("\n=== 功能注释分析完成 ===")
    else:
        print("无法加载Hub蛋白数据，请先运行PPI网络分析")

if __name__ == "__main__":
    main()
