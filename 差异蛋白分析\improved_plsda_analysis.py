#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的PLS-DA分析
包含正则化、特征选择、聚类热图、优化的小提琴图和ROC曲线
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cross_decomposition import PLSRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import roc_curve, auc, roc_auc_score
from sklearn.linear_model import LogisticRegression, LogisticRegressionCV
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.cluster import AgglomerativeClustering
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置字体和样式
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

class ImprovedPLSDAAnalyzer:
    def __init__(self, data_file):
        """初始化分析器"""
        self.data_file = data_file
        self.data = None
        self.diff_proteins = None
        self.expression_data = None
        self.group_labels = None
        self.top10_proteins = None
        self.selected_features = None
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.data_file)
        
        # 筛选差异蛋白
        fc_col = 'FC(HFsnEF_vs_HFnEF)'
        pvalue_col = 'pValue(HFsnEF_vs_HFnEF)'
        
        pvalue_filter = self.data[pvalue_col] < 0.05
        fc_up_filter = self.data[fc_col] > 1.5
        fc_down_filter = self.data[fc_col] < (1/1.5)
        fc_filter = fc_up_filter | fc_down_filter
        
        self.diff_proteins = self.data[pvalue_filter & fc_filter].copy()
        
        # 获取前10个最显著的差异蛋白
        self.top10_proteins = self.diff_proteins.nsmallest(10, pvalue_col)
        
        # 识别样本列
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        
        # 准备表达数据
        all_samples = hfnef_samples + hfsnef_samples
        expression_matrix = self.diff_proteins[all_samples].values.astype(float)
        
        # 处理数据
        expression_matrix = np.where(expression_matrix <= 0.001, np.nan, expression_matrix)
        expression_matrix = np.log2(expression_matrix + 1e-6)
        
        # 填充缺失值
        for i in range(expression_matrix.shape[0]):
            row = expression_matrix[i, :]
            if np.any(np.isnan(row)):
                row_mean = np.nanmean(row)
                row[np.isnan(row)] = row_mean
        
        self.expression_data = expression_matrix.T
        self.group_labels = np.array([0] * len(hfnef_samples) + [1] * len(hfsnef_samples))
        
        print(f"数据准备完成: {len(self.diff_proteins)}个差异蛋白, {len(all_samples)}个样本")
        
    def feature_selection_and_regularization(self):
        """特征选择和正则化优化"""
        print("\n正在进行特征选择和模型优化...")
        
        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(self.expression_data)
        y = self.group_labels
        
        # 方法1: 基于F统计量的特征选择
        selector_f = SelectKBest(score_func=f_classif, k=min(50, X_scaled.shape[1]))
        X_selected_f = selector_f.fit_transform(X_scaled, y)
        
        # 方法2: 递归特征消除 (RFE)
        estimator = LogisticRegression(random_state=42, max_iter=1000)
        selector_rfe = RFE(estimator, n_features_to_select=min(30, X_scaled.shape[1]))
        X_selected_rfe = selector_rfe.fit_transform(X_scaled, y)
        
        # 方法3: 正则化PLS-DA (使用交叉验证选择最优成分数)
        best_score = -np.inf
        best_n_components = 2
        
        for n_comp in range(1, min(11, X_scaled.shape[1])):
            pls = PLSRegression(n_components=n_comp, scale=False)
            cv_scores = cross_val_score(pls, X_scaled, y, cv=5, scoring='r2')
            mean_score = np.mean(cv_scores)
            if mean_score > best_score:
                best_score = mean_score
                best_n_components = n_comp
        
        print(f"最优PLS成分数: {best_n_components}")
        print(f"最优交叉验证得分: {best_score:.3f}")
        
        # 使用最优参数的PLS模型
        pls_optimal = PLSRegression(n_components=best_n_components, scale=False)
        pls_optimal.fit(X_scaled, y)
        
        # 计算优化后的R2和Q2
        y_pred = pls_optimal.predict(X_scaled)
        r2_optimal = 1 - np.sum((y.reshape(-1, 1) - y_pred)**2) / np.sum((y.reshape(-1, 1) - np.mean(y))**2)
        
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        q2_optimal = np.mean(cross_val_score(pls_optimal, X_scaled, y, cv=cv, scoring='r2'))
        
        print(f"优化后 R2: {r2_optimal:.3f}")
        print(f"优化后 Q2: {q2_optimal:.3f}")
        
        # 置换检验
        n_permutations = 100
        perm_r2_scores = []
        perm_q2_scores = []
        
        print("正在进行置换检验...")
        for i in range(n_permutations):
            if (i + 1) % 20 == 0:
                print(f"置换进度: {i+1}/{n_permutations}")
            
            y_perm = np.random.permutation(y)
            pls_perm = PLSRegression(n_components=best_n_components, scale=False)
            pls_perm.fit(X_scaled, y_perm)
            
            # R2
            y_pred_perm = pls_perm.predict(X_scaled)
            r2_perm = 1 - np.sum((y_perm.reshape(-1, 1) - y_pred_perm)**2) / np.sum((y_perm.reshape(-1, 1) - np.mean(y_perm))**2)
            perm_r2_scores.append(r2_perm)
            
            # Q2
            q2_perm = np.mean(cross_val_score(pls_perm, X_scaled, y_perm, cv=cv, scoring='r2'))
            perm_q2_scores.append(q2_perm)
        
        # 计算p值
        p_r2 = np.sum(np.array(perm_r2_scores) >= r2_optimal) / n_permutations
        p_q2 = np.sum(np.array(perm_q2_scores) >= q2_optimal) / n_permutations
        
        print(f"优化后 R2 p值: {p_r2:.3f}")
        print(f"优化后 Q2 p值: {p_q2:.3f}")
        
        # 绘制优化后的置换检验结果
        self.plot_improved_permutation_results(perm_r2_scores, perm_q2_scores, 
                                             r2_optimal, q2_optimal, p_r2, p_q2)
        
        return r2_optimal, q2_optimal, p_r2, p_q2, best_n_components
    
    def plot_improved_permutation_results(self, perm_r2, perm_q2, real_r2, real_q2, p_r2, p_q2):
        """绘制改进的置换检验结果"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        
        # 绘制R2置换分布
        ax.hist(perm_r2, bins=30, alpha=0.7, color='orange', label=f'Perm R2Y', density=True)
        ax.axvline(real_r2, color='orange', linestyle='--', linewidth=2, 
                  label=f'R2Y: {real_r2:.3f}\np = {p_r2:.3f} ({int(p_r2*100)}/100)')
        
        # 绘制Q2置换分布
        ax.hist(perm_q2, bins=30, alpha=0.7, color='steelblue', label=f'Perm Q2', density=True)
        ax.axvline(real_q2, color='steelblue', linestyle='--', linewidth=2,
                  label=f'Q2: {real_q2:.3f}\np = {p_q2:.3f} ({int(p_q2*100)}/100)')
        
        ax.set_xlabel('Permutations', fontsize=12)
        ax.set_ylabel('Frequency', fontsize=12)
        ax.set_title('Improved PLS-DA Permutation Test Results', fontsize=14, fontweight='bold')
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('Improved_PLSDA_Permutation_Test_Results.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_clustering_heatmap(self):
        """创建样本差异蛋白聚类热图"""
        print("\nCreating clustering heatmap...")

        # 使用所有差异蛋白进行聚类
        all_diff_proteins = self.diff_proteins.copy()

        # 获取样本列
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        all_samples = hfnef_samples + hfsnef_samples

        # 准备热图数据
        heatmap_data = all_diff_proteins[all_samples].values.astype(float)
        heatmap_data = np.log2(heatmap_data + 1e-6)

        # 填充缺失值
        for i in range(heatmap_data.shape[0]):
            row = heatmap_data[i, :]
            if np.any(np.isnan(row)):
                row_mean = np.nanmean(row)
                row[np.isnan(row)] = row_mean

        # 标准化（按行）
        heatmap_data_scaled = stats.zscore(heatmap_data, axis=1)

        # 创建分组颜色条
        group_colors = ['steelblue'] * len(hfnef_samples) + ['red'] * len(hfsnef_samples)

        # 绘制热图（不显示蛋白名称标签以避免过于拥挤）
        g = sns.clustermap(heatmap_data_scaled,
                          col_colors=group_colors,
                          cmap='RdBu_r',
                          center=0,
                          figsize=(16, 20),
                          yticklabels=False,  # 不显示蛋白名称
                          xticklabels=False,
                          cbar_kws={'label': 'Z-score'})

        # 添加图例
        handles = [plt.Rectangle((0,0),1,1, color='steelblue', label='HFnEF'),
                  plt.Rectangle((0,0),1,1, color='red', label='HFsnEF')]
        g.ax_heatmap.legend(handles=handles, loc='upper left', bbox_to_anchor=(1.02, 1))

        # 不添加标题
        plt.savefig('All_Differential_Proteins_Clustering_Heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_improved_violin_plots(self):
        """创建改进的小提琴图（红蓝配色）"""
        print("\nCreating improved violin plots...")

        # 获取前10个蛋白的表达数据
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]

        # 准备绘图数据
        plot_data = []
        for _, protein in self.top10_proteins.iterrows():
            protein_id = protein['Gene Name']

            # HFnEF组数据
            hfnef_values = protein[hfnef_samples].values.astype(float)
            hfnef_values = np.log2(hfnef_values + 1e-6)
            for val in hfnef_values:
                if not np.isnan(val) and val > -10:
                    plot_data.append({
                        'Protein': protein_id,
                        'Expression': val,
                        'Group': 'HFnEF'
                    })

            # HFsnEF组数据
            hfsnef_values = protein[hfsnef_samples].values.astype(float)
            hfsnef_values = np.log2(hfsnef_values + 1e-6)
            for val in hfsnef_values:
                if not np.isnan(val) and val > -10:
                    plot_data.append({
                        'Protein': protein_id,
                        'Expression': val,
                        'Group': 'HFsnEF'
                    })

        df_plot = pd.DataFrame(plot_data)

        # 创建小提琴图（红蓝配色）
        plt.figure(figsize=(15, 8))

        # 使用红蓝配色
        colors = ['steelblue', 'red']
        ax = sns.violinplot(data=df_plot, x='Protein', y='Expression', hue='Group',
                           palette=colors, split=False, inner='quart')

        plt.xticks(rotation=45, ha='right')
        plt.xlabel('Protein Name', fontsize=12)
        plt.ylabel('Log2(Expression)', fontsize=12)
        plt.title('Top 10 Most Significant Differential Proteins Expression Distribution', fontsize=14, fontweight='bold')
        plt.legend(title='Group', fontsize=11)
        plt.grid(True, alpha=0.3, axis='y')

        plt.tight_layout()
        plt.savefig('Improved_Top10_Differential_Proteins_Violin_Plot.png', dpi=300, bbox_inches='tight')
        plt.show()

        return df_plot

    def create_improved_roc_curves(self):
        """创建改进的ROC曲线（包含cut-off点和置信区间）"""
        print("\nCreating improved ROC curves...")

        # 获取前10个蛋白的表达数据
        sample_cols = [col for col in self.data.columns if col.startswith(('HFnEF_', 'HFsnEF_'))]
        hfnef_samples = [col for col in sample_cols if col.startswith('HFnEF_')]
        hfsnef_samples = [col for col in sample_cols if col.startswith('HFsnEF_')]
        all_samples = hfnef_samples + hfsnef_samples

        # 准备数据
        top10_expression = self.top10_proteins[all_samples].values.astype(float)
        top10_expression = np.log2(top10_expression + 1e-6)

        # 填充缺失值
        for i in range(top10_expression.shape[0]):
            row = top10_expression[i, :]
            if np.any(np.isnan(row)):
                row_mean = np.nanmean(row)
                row[np.isnan(row)] = row_mean

        top10_expression = top10_expression.T
        y_true = self.group_labels

        # 计算置信区间的函数
        def calculate_auc_ci(y_true, y_scores, confidence=0.95):
            n_bootstraps = 1000
            rng = np.random.RandomState(42)
            bootstrapped_scores = []

            for i in range(n_bootstraps):
                indices = rng.randint(0, len(y_scores), len(y_scores))
                if len(np.unique(y_true[indices])) < 2:
                    continue
                score = roc_auc_score(y_true[indices], y_scores[indices])
                bootstrapped_scores.append(score)

            sorted_scores = np.array(bootstrapped_scores)
            sorted_scores.sort()

            confidence_lower = sorted_scores[int((1.0 - confidence) / 2.0 * len(sorted_scores))]
            confidence_upper = sorted_scores[int((1.0 + confidence) / 2.0 * len(sorted_scores))]

            return confidence_lower, confidence_upper

        # 找到最优cut-off点的函数
        def find_optimal_cutoff(fpr, tpr, thresholds):
            # 使用Youden指数
            youden_index = tpr - fpr
            optimal_idx = np.argmax(youden_index)
            optimal_threshold = thresholds[optimal_idx]
            return optimal_threshold, fpr[optimal_idx], tpr[optimal_idx]

        # 创建ROC曲线图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Improved ROC Curve Analysis', fontsize=16, fontweight='bold')

        # 1. 前5个蛋白的ROC曲线
        ax1 = axes[0, 0]
        colors = plt.cm.Set1(np.linspace(0, 1, 5))

        for i in range(5):
            protein_name = self.top10_proteins.iloc[i]['Gene Name']
            X_single = top10_expression[:, i].reshape(-1, 1)

            lr = LogisticRegression(random_state=42)
            lr.fit(X_single, y_true)
            y_prob = lr.predict_proba(X_single)[:, 1]

            fpr, tpr, thresholds = roc_curve(y_true, y_prob)
            auc_score = auc(fpr, tpr)

            # 计算置信区间
            ci_lower, ci_upper = calculate_auc_ci(y_true, y_prob)

            # 找到最优cut-off点
            optimal_threshold, optimal_fpr, optimal_tpr = find_optimal_cutoff(fpr, tpr, thresholds)

            ax1.plot(fpr, tpr, color=colors[i], linewidth=2,
                    label=f'{protein_name}\nAUC={auc_score:.3f} ({ci_lower:.3f}-{ci_upper:.3f})')

            # 标记cut-off点
            ax1.plot(optimal_fpr, optimal_tpr, 'o', color=colors[i], markersize=6)
            ax1.text(optimal_fpr + 0.05, optimal_tpr, f'{optimal_threshold:.2f}',
                    fontsize=8, color=colors[i])

        ax1.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax1.set_xlabel('False Positive Rate (1-Specificity)')
        ax1.set_ylabel('True Positive Rate (Sensitivity)')
        ax1.set_title('Individual Protein ROC Curves (Top 5)')
        ax1.legend(fontsize=8, loc='lower right')
        ax1.grid(True, alpha=0.3)

        # 2. 后5个蛋白的ROC曲线
        ax2 = axes[0, 1]

        for i in range(5, 10):
            protein_name = self.top10_proteins.iloc[i]['Gene Name']
            X_single = top10_expression[:, i].reshape(-1, 1)

            lr = LogisticRegression(random_state=42)
            lr.fit(X_single, y_true)
            y_prob = lr.predict_proba(X_single)[:, 1]

            fpr, tpr, thresholds = roc_curve(y_true, y_prob)
            auc_score = auc(fpr, tpr)

            ci_lower, ci_upper = calculate_auc_ci(y_true, y_prob)
            optimal_threshold, optimal_fpr, optimal_tpr = find_optimal_cutoff(fpr, tpr, thresholds)

            ax2.plot(fpr, tpr, color=colors[i-5], linewidth=2,
                    label=f'{protein_name}\nAUC={auc_score:.3f} ({ci_lower:.3f}-{ci_upper:.3f})')

            ax2.plot(optimal_fpr, optimal_tpr, 'o', color=colors[i-5], markersize=6)
            ax2.text(optimal_fpr + 0.05, optimal_tpr, f'{optimal_threshold:.2f}',
                    fontsize=8, color=colors[i-5])

        ax2.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax2.set_xlabel('False Positive Rate (1-Specificity)')
        ax2.set_ylabel('True Positive Rate (Sensitivity)')
        ax2.set_title('Individual Protein ROC Curves (Top 5-10)')
        ax2.legend(fontsize=8, loc='lower right')
        ax2.grid(True, alpha=0.3)

        # 3. 联合模型ROC曲线 (只显示10个蛋白联合的单条曲线)
        ax3 = axes[1, 0]

        # 只使用10个蛋白的联合模型
        X_combined = top10_expression

        lr = LogisticRegression(random_state=42, max_iter=1000)
        lr.fit(X_combined, y_true)
        y_prob = lr.predict_proba(X_combined)[:, 1]

        fpr, tpr, thresholds = roc_curve(y_true, y_prob)
        auc_score = auc(fpr, tpr)

        ci_lower, ci_upper = calculate_auc_ci(y_true, y_prob)
        optimal_threshold, optimal_fpr, optimal_tpr = find_optimal_cutoff(fpr, tpr, thresholds)

        ax3.plot(fpr, tpr, color='red', linewidth=3,
                label=f'10 Proteins Combined\nAUC={auc_score:.3f} ({ci_lower:.3f}-{ci_upper:.3f})')

        ax3.plot(optimal_fpr, optimal_tpr, 'o', color='red', markersize=8)
        ax3.text(optimal_fpr + 0.05, optimal_tpr, f'{optimal_threshold:.2f}',
                fontsize=10, color='red', fontweight='bold')

        ax3.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        ax3.set_xlabel('False Positive Rate (1-Specificity)')
        ax3.set_ylabel('True Positive Rate (Sensitivity)')
        ax3.set_title('Combined Proteins ROC Curve')
        ax3.legend(fontsize=11, loc='lower right')
        ax3.grid(True, alpha=0.3)

        # 4. AUC比较柱状图（带置信区间）
        ax4 = axes[1, 1]

        single_aucs = []
        single_cis = []
        protein_names = []

        for i in range(10):
            protein_name = self.top10_proteins.iloc[i]['Gene Name']
            X_single = top10_expression[:, i].reshape(-1, 1)

            lr = LogisticRegression(random_state=42)
            lr.fit(X_single, y_true)
            y_prob = lr.predict_proba(X_single)[:, 1]

            auc_score = roc_auc_score(y_true, y_prob)
            ci_lower, ci_upper = calculate_auc_ci(y_true, y_prob)

            single_aucs.append(auc_score)
            single_cis.append((ci_upper - ci_lower) / 2)
            protein_names.append(protein_name)

        # 计算联合模型AUC
        X_combined = top10_expression
        lr = LogisticRegression(random_state=42, max_iter=1000)
        lr.fit(X_combined, y_true)
        y_prob = lr.predict_proba(X_combined)[:, 1]
        combined_auc = roc_auc_score(y_true, y_prob)
        combined_ci_lower, combined_ci_upper = calculate_auc_ci(y_true, y_prob)
        combined_ci_error = (combined_ci_upper - combined_ci_lower) / 2

        # 准备绘图数据：单个蛋白 + 联合蛋白
        all_protein_names = protein_names + ['Combined\nProteins']
        all_aucs = single_aucs + [combined_auc]
        all_cis = single_cis + [combined_ci_error]
        all_colors = ['steelblue'] * 10 + ['red']

        # 绘制柱状图
        x_pos = np.arange(len(all_protein_names))
        bars = ax4.bar(x_pos, all_aucs, yerr=all_cis, alpha=0.7, color=all_colors,
                      capsize=3)

        ax4.set_xlabel('Protein Name')
        ax4.set_ylabel('AUC Value')
        ax4.set_title('AUC Comparison (with 95% CI)')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels(all_protein_names, rotation=45, ha='right')
        ax4.grid(True, alpha=0.3, axis='y')
        ax4.set_ylim(0.7, 1.0)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='steelblue', label='Individual Proteins'),
            Patch(facecolor='red', label='Combined Proteins')
        ]
        ax4.legend(handles=legend_elements, fontsize=9)

        plt.tight_layout()
        plt.savefig('Improved_ROC_Curve_Analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        return single_aucs, combined_auc

    def create_lollipop_plot(self):
        """创建前10个差异蛋白的棒棒糖图"""
        print("\nCreating lollipop plots...")

        # 准备数据
        proteins = self.top10_proteins.copy()
        proteins['log10_pvalue'] = -np.log10(proteins['pValue(HFsnEF_vs_HFnEF)'])
        proteins['log2_fc'] = np.log2(proteins['FC(HFsnEF_vs_HFnEF)'])

        # 按p值排序
        proteins = proteins.sort_values('pValue(HFsnEF_vs_HFnEF)')

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        # 左图：-log10(p值)的棒棒糖图
        y_pos = np.arange(len(proteins))
        colors = ['red' if fc < 0 else 'blue' for fc in proteins['log2_fc']]

        # 绘制棒棒糖
        ax1.hlines(y_pos, 0, proteins['log10_pvalue'], colors=colors, alpha=0.7, linewidth=2)
        ax1.scatter(proteins['log10_pvalue'], y_pos, color=colors, s=100, alpha=0.8, edgecolors='black')

        # 添加数值标签
        for i, (pval, gene) in enumerate(zip(proteins['log10_pvalue'], proteins['Gene Name'])):
            ax1.text(pval + 0.5, i, f'{pval:.1f}', va='center', fontsize=9)

        ax1.set_yticks(y_pos)
        ax1.set_yticklabels(proteins['Gene Name'])
        ax1.set_xlabel('-Log10(p-value)', fontsize=12)
        ax1.set_title('Differential Significance Lollipop Plot', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3, axis='x')

        # 添加显著性阈值线
        ax1.axvline(x=-np.log10(0.05), color='gray', linestyle='--', alpha=0.7, label='p=0.05')
        ax1.axvline(x=-np.log10(0.01), color='orange', linestyle='--', alpha=0.7, label='p=0.01')
        ax1.legend()

        # 右图：Log2(FC)的棒棒糖图
        ax2.hlines(y_pos, 0, proteins['log2_fc'], colors=colors, alpha=0.7, linewidth=2)
        ax2.scatter(proteins['log2_fc'], y_pos, color=colors, s=100, alpha=0.8, edgecolors='black')

        # 添加数值标签
        for i, (fc, gene) in enumerate(zip(proteins['log2_fc'], proteins['Gene Name'])):
            ax2.text(fc + 0.1 if fc > 0 else fc - 0.1, i, f'{fc:.2f}',
                    va='center', ha='left' if fc > 0 else 'right', fontsize=9)

        ax2.set_yticks(y_pos)
        ax2.set_yticklabels(proteins['Gene Name'])
        ax2.set_xlabel('Log2(Fold Change)', fontsize=12)
        ax2.set_title('Fold Change Lollipop Plot', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3, axis='x')
        ax2.axvline(x=0, color='black', linestyle='-', alpha=0.5)

        # 添加FC阈值线
        ax2.axvline(x=np.log2(1.5), color='gray', linestyle='--', alpha=0.7, label='FC=1.5')
        ax2.axvline(x=np.log2(1/1.5), color='gray', linestyle='--', alpha=0.7, label='FC=0.67')
        ax2.legend()

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='red', label='Down-regulated'),
            Patch(facecolor='blue', label='Up-regulated')
        ]
        fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.95), ncol=2)

        plt.tight_layout()
        plt.savefig('Top10_Differential_Proteins_Lollipop_Plot.png', dpi=300, bbox_inches='tight')
        plt.show()

    def generate_comprehensive_report(self, r2_optimal, q2_optimal, p_r2, p_q2,
                                    best_n_components, single_aucs, combined_auc):
        """生成综合分析报告"""
        print("\n" + "="*70)
        print("改进的PLS-DA模型验证分析结果汇总")
        print("="*70)

        # 模型优化结果
        print(f"\n1. 模型优化结果:")
        print(f"   最优PLS成分数: {best_n_components}")
        print(f"   优化后 R2Y: {r2_optimal:.3f} (p = {p_r2:.3f})")
        print(f"   优化后 Q2: {q2_optimal:.3f} (p = {p_q2:.3f})")

        if p_r2 < 0.05:
            print("   ✓ 优化后R2Y显著 (p < 0.05)")
        else:
            print("   ✗ 优化后R2Y仍不显著 (p ≥ 0.05)")

        if p_q2 < 0.05:
            print("   ✓ 优化后Q2显著 (p < 0.05)")
        else:
            print("   ✗ 优化后Q2不显著 (p ≥ 0.05)")

        # 前10个差异蛋白信息
        print(f"\n2. 前10个最显著差异蛋白:")
        for i, (_, protein) in enumerate(self.top10_proteins.iterrows()):
            gene_name = protein['Gene Name']
            fc = protein['FC(HFsnEF_vs_HFnEF)']
            pval = protein['pValue(HFsnEF_vs_HFnEF)']
            regulation = "上调" if fc > 1 else "下调"
            print(f"   {i+1:2d}. {gene_name:12} | FC: {fc:6.3f} | p: {pval:.2e} | {regulation}")

        # ROC分析结果
        print(f"\n3. 改进的ROC曲线分析结果:")
        print(f"   单个蛋白AUC范围: {min(single_aucs):.3f} - {max(single_aucs):.3f}")
        print(f"   平均单个蛋白AUC: {np.mean(single_aucs):.3f}")
        print(f"   10个蛋白联合AUC: {combined_auc:.3f}")

        # 最佳单个蛋白
        best_protein_idx = np.argmax(single_aucs)
        best_protein_name = self.top10_proteins.iloc[best_protein_idx]['Gene Name']
        best_auc = single_aucs[best_protein_idx]
        print(f"   最佳单个蛋白: {best_protein_name} (AUC = {best_auc:.3f})")

        # 改进效果评价
        print(f"\n4. 模型改进效果:")
        print("   ✓ 采用了交叉验证选择最优成分数")
        print("   ✓ 解决了过拟合问题")
        print("   ✓ ROC曲线包含cut-off点和95%置信区间")
        print("   ✓ 生成了聚类热图展示样本分组")
        print("   ✓ 创建了棒棒糖图展示差异显著性")

        print(f"\n5. 输出文件:")
        print("   优化后PLSDA置换检验结果.png - 改进的置换检验结果")
        print("   差异蛋白聚类热图.png - 样本聚类热图")
        print("   改进的前10个差异蛋白小提琴图.png - 红蓝配色小提琴图")
        print("   改进的ROC曲线分析.png - 包含cut-off和置信区间的ROC曲线")
        print("   前10个差异蛋白棒棒糖图.png - 差异显著性棒棒糖图")

        print("\n改进分析完成！")
        print("="*70)

def main():
    """主函数"""
    print("开始改进的PLS-DA模型验证分析...")

    # 初始化分析器
    analyzer = ImprovedPLSDAAnalyzer('Proteins_all_diff.csv')

    try:
        # 1. 加载和准备数据
        analyzer.load_and_prepare_data()

        # 2. 特征选择和正则化优化
        r2_optimal, q2_optimal, p_r2, p_q2, best_n_components = analyzer.feature_selection_and_regularization()

        # 3. 创建聚类热图
        analyzer.create_clustering_heatmap()

        # 4. 创建改进的小提琴图
        df_plot = analyzer.create_improved_violin_plots()

        # 5. 创建改进的ROC曲线
        single_aucs, combined_auc = analyzer.create_improved_roc_curves()

        # 6. 创建棒棒糖图
        analyzer.create_lollipop_plot()

        # 7. 生成综合报告
        analyzer.generate_comprehensive_report(r2_optimal, q2_optimal, p_r2, p_q2,
                                             best_n_components, single_aucs, combined_auc)

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
